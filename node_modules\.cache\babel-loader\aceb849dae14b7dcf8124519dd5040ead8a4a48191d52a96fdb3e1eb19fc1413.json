{"ast": null, "code": "export const validateBlockListParams = (list, watchVariable) => {\n  if (!Array.isArray(list)) {\n    throw 'The BlockList list has to be an array';\n  }\n  if (typeof watchVariable !== 'string') {\n    throw 'The BlockList watchVariable has to be a string';\n  }\n};", "map": {"version": 3, "names": ["validateBlockListParams", "list", "watchVariable", "Array", "isArray"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/validateBlockListParams/validateBlockListParams.js"], "sourcesContent": ["export const validateBlockListParams = (list, watchVariable) => {\n    if (!Array.isArray(list)) {\n        throw 'The BlockList list has to be an array';\n    }\n    if (typeof watchVariable !== 'string') {\n        throw 'The BlockList watchVariable has to be a string';\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,uBAAuB,GAAGA,CAACC,IAAI,EAAEC,aAAa,KAAK;EAC5D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IACtB,MAAM,uCAAuC;EACjD;EACA,IAAI,OAAOC,aAAa,KAAK,QAAQ,EAAE;IACnC,MAAM,gDAAgD;EAC1D;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}