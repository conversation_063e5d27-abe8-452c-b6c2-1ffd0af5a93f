{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _lodash = require('lodash.throttle');\nvar _lodash2 = _interopRequireDefault(_lodash);\nvar _passiveEventListeners = require('./passive-event-listeners');\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\n// The eventHandler will execute at a rate of 15fps by default\nvar eventThrottler = function eventThrottler(eventHandler) {\n  var throttleAmount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 66;\n  return (0, _lodash2.default)(eventHandler, throttleAmount);\n};\nvar scrollSpy = {\n  spyCallbacks: [],\n  spySetState: [],\n  scrollSpyContainers: [],\n  mount: function mount(scrollSpyContainer, throttle) {\n    if (scrollSpyContainer) {\n      var eventHandler = eventThrottler(function (event) {\n        scrollSpy.scrollHandler(scrollSpyContainer);\n      }, throttle);\n      scrollSpy.scrollSpyContainers.push(scrollSpyContainer);\n      (0, _passiveEventListeners.addPassiveEventListener)(scrollSpyContainer, 'scroll', eventHandler);\n      return function () {\n        (0, _passiveEventListeners.removePassiveEventListener)(scrollSpyContainer, 'scroll', eventHandler);\n        scrollSpy.scrollSpyContainers.splice(scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer), 1);\n      };\n    }\n    return function () {};\n  },\n  isMounted: function isMounted(scrollSpyContainer) {\n    return scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer) !== -1;\n  },\n  currentPositionX: function currentPositionX(scrollSpyContainer) {\n    if (scrollSpyContainer === document) {\n      var supportPageOffset = window.scrollY !== undefined;\n      var isCSS1Compat = (document.compatMode || \"\") === \"CSS1Compat\";\n      return supportPageOffset ? window.scrollX : isCSS1Compat ? document.documentElement.scrollLeft : document.body.scrollLeft;\n    } else {\n      return scrollSpyContainer.scrollLeft;\n    }\n  },\n  currentPositionY: function currentPositionY(scrollSpyContainer) {\n    if (scrollSpyContainer === document) {\n      var supportPageOffset = window.scrollX !== undefined;\n      var isCSS1Compat = (document.compatMode || \"\") === \"CSS1Compat\";\n      return supportPageOffset ? window.scrollY : isCSS1Compat ? document.documentElement.scrollTop : document.body.scrollTop;\n    } else {\n      return scrollSpyContainer.scrollTop;\n    }\n  },\n  scrollHandler: function scrollHandler(scrollSpyContainer) {\n    var callbacks = scrollSpy.scrollSpyContainers[scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer)].spyCallbacks || [];\n    callbacks.forEach(function (c) {\n      return c(scrollSpy.currentPositionX(scrollSpyContainer), scrollSpy.currentPositionY(scrollSpyContainer));\n    });\n  },\n  addStateHandler: function addStateHandler(handler) {\n    scrollSpy.spySetState.push(handler);\n  },\n  addSpyHandler: function addSpyHandler(handler, scrollSpyContainer) {\n    var container = scrollSpy.scrollSpyContainers[scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer)];\n    if (!container.spyCallbacks) {\n      container.spyCallbacks = [];\n    }\n    container.spyCallbacks.push(handler);\n  },\n  updateStates: function updateStates() {\n    scrollSpy.spySetState.forEach(function (s) {\n      return s();\n    });\n  },\n  unmount: function unmount(stateHandler, spyHandler) {\n    scrollSpy.scrollSpyContainers.forEach(function (c) {\n      return c.spyCallbacks && c.spyCallbacks.length && c.spyCallbacks.indexOf(spyHandler) > -1 && c.spyCallbacks.splice(c.spyCallbacks.indexOf(spyHandler), 1);\n    });\n    if (scrollSpy.spySetState && scrollSpy.spySetState.length && scrollSpy.spySetState.indexOf(stateHandler) > -1) {\n      scrollSpy.spySetState.splice(scrollSpy.spySetState.indexOf(stateHandler), 1);\n    }\n    document.removeEventListener('scroll', scrollSpy.scrollHandler);\n  },\n  update: function update() {\n    return scrollSpy.scrollSpyContainers.forEach(function (c) {\n      return scrollSpy.scrollHandler(c);\n    });\n  }\n};\nexports.default = scrollSpy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_lodash", "require", "_lodash2", "_interopRequireDefault", "_passiveEventListeners", "obj", "__esModule", "default", "eventThrottler", "<PERSON><PERSON><PERSON><PERSON>", "throttleAmount", "arguments", "length", "undefined", "scrollSpy", "spyCallbacks", "spySetState", "scrollSpyContainers", "mount", "scrollSpyContainer", "throttle", "event", "<PERSON><PERSON><PERSON><PERSON>", "push", "addPassiveEventListener", "removePassiveEventListener", "splice", "indexOf", "isMounted", "currentPositionX", "document", "supportPageOffset", "window", "scrollY", "isCSS1Compat", "compatMode", "scrollX", "documentElement", "scrollLeft", "body", "currentPositionY", "scrollTop", "callbacks", "for<PERSON>ach", "c", "addStateHandler", "handler", "addSpyHandler", "container", "updateStates", "s", "unmount", "state<PERSON><PERSON><PERSON>", "spyHandler", "removeEventListener", "update"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/react-scroll/modules/mixins/scroll-spy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _lodash = require('lodash.throttle');\n\nvar _lodash2 = _interopRequireDefault(_lodash);\n\nvar _passiveEventListeners = require('./passive-event-listeners');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// The eventHandler will execute at a rate of 15fps by default\nvar eventThrottler = function eventThrottler(eventHandler) {\n  var throttleAmount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 66;\n  return (0, _lodash2.default)(eventHandler, throttleAmount);\n};\n\nvar scrollSpy = {\n\n  spyCallbacks: [],\n  spySetState: [],\n  scrollSpyContainers: [],\n\n  mount: function mount(scrollSpyContainer, throttle) {\n    if (scrollSpyContainer) {\n      var eventHandler = eventThrottler(function (event) {\n        scrollSpy.scrollHandler(scrollSpyContainer);\n      }, throttle);\n      scrollSpy.scrollSpyContainers.push(scrollSpyContainer);\n      (0, _passiveEventListeners.addPassiveEventListener)(scrollSpyContainer, 'scroll', eventHandler);\n      return function () {\n        (0, _passiveEventListeners.removePassiveEventListener)(scrollSpyContainer, 'scroll', eventHandler);\n        scrollSpy.scrollSpyContainers.splice(scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer), 1);\n      };\n    }\n    return function () {};\n  },\n  isMounted: function isMounted(scrollSpyContainer) {\n    return scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer) !== -1;\n  },\n  currentPositionX: function currentPositionX(scrollSpyContainer) {\n    if (scrollSpyContainer === document) {\n      var supportPageOffset = window.scrollY !== undefined;\n      var isCSS1Compat = (document.compatMode || \"\") === \"CSS1Compat\";\n      return supportPageOffset ? window.scrollX : isCSS1Compat ? document.documentElement.scrollLeft : document.body.scrollLeft;\n    } else {\n      return scrollSpyContainer.scrollLeft;\n    }\n  },\n  currentPositionY: function currentPositionY(scrollSpyContainer) {\n    if (scrollSpyContainer === document) {\n      var supportPageOffset = window.scrollX !== undefined;\n      var isCSS1Compat = (document.compatMode || \"\") === \"CSS1Compat\";\n      return supportPageOffset ? window.scrollY : isCSS1Compat ? document.documentElement.scrollTop : document.body.scrollTop;\n    } else {\n      return scrollSpyContainer.scrollTop;\n    }\n  },\n  scrollHandler: function scrollHandler(scrollSpyContainer) {\n    var callbacks = scrollSpy.scrollSpyContainers[scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer)].spyCallbacks || [];\n    callbacks.forEach(function (c) {\n      return c(scrollSpy.currentPositionX(scrollSpyContainer), scrollSpy.currentPositionY(scrollSpyContainer));\n    });\n  },\n  addStateHandler: function addStateHandler(handler) {\n    scrollSpy.spySetState.push(handler);\n  },\n  addSpyHandler: function addSpyHandler(handler, scrollSpyContainer) {\n    var container = scrollSpy.scrollSpyContainers[scrollSpy.scrollSpyContainers.indexOf(scrollSpyContainer)];\n\n    if (!container.spyCallbacks) {\n      container.spyCallbacks = [];\n    }\n\n    container.spyCallbacks.push(handler);\n  },\n  updateStates: function updateStates() {\n    scrollSpy.spySetState.forEach(function (s) {\n      return s();\n    });\n  },\n  unmount: function unmount(stateHandler, spyHandler) {\n    scrollSpy.scrollSpyContainers.forEach(function (c) {\n      return c.spyCallbacks && c.spyCallbacks.length && c.spyCallbacks.indexOf(spyHandler) > -1 && c.spyCallbacks.splice(c.spyCallbacks.indexOf(spyHandler), 1);\n    });\n\n    if (scrollSpy.spySetState && scrollSpy.spySetState.length && scrollSpy.spySetState.indexOf(stateHandler) > -1) {\n      scrollSpy.spySetState.splice(scrollSpy.spySetState.indexOf(stateHandler), 1);\n    }\n\n    document.removeEventListener('scroll', scrollSpy.scrollHandler);\n  },\n\n\n  update: function update() {\n    return scrollSpy.scrollSpyContainers.forEach(function (c) {\n      return scrollSpy.scrollHandler(c);\n    });\n  }\n};\n\nexports.default = scrollSpy;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAExC,IAAIC,QAAQ,GAAGC,sBAAsB,CAACH,OAAO,CAAC;AAE9C,IAAII,sBAAsB,GAAGH,OAAO,CAAC,2BAA2B,CAAC;AAEjE,SAASE,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;;AAE9F;AACA,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,YAAY,EAAE;EACzD,IAAIC,cAAc,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAC3F,OAAO,CAAC,CAAC,EAAET,QAAQ,CAACK,OAAO,EAAEE,YAAY,EAAEC,cAAc,CAAC;AAC5D,CAAC;AAED,IAAII,SAAS,GAAG;EAEdC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE,EAAE;EACfC,mBAAmB,EAAE,EAAE;EAEvBC,KAAK,EAAE,SAASA,KAAKA,CAACC,kBAAkB,EAAEC,QAAQ,EAAE;IAClD,IAAID,kBAAkB,EAAE;MACtB,IAAIV,YAAY,GAAGD,cAAc,CAAC,UAAUa,KAAK,EAAE;QACjDP,SAAS,CAACQ,aAAa,CAACH,kBAAkB,CAAC;MAC7C,CAAC,EAAEC,QAAQ,CAAC;MACZN,SAAS,CAACG,mBAAmB,CAACM,IAAI,CAACJ,kBAAkB,CAAC;MACtD,CAAC,CAAC,EAAEf,sBAAsB,CAACoB,uBAAuB,EAAEL,kBAAkB,EAAE,QAAQ,EAAEV,YAAY,CAAC;MAC/F,OAAO,YAAY;QACjB,CAAC,CAAC,EAAEL,sBAAsB,CAACqB,0BAA0B,EAAEN,kBAAkB,EAAE,QAAQ,EAAEV,YAAY,CAAC;QAClGK,SAAS,CAACG,mBAAmB,CAACS,MAAM,CAACZ,SAAS,CAACG,mBAAmB,CAACU,OAAO,CAACR,kBAAkB,CAAC,EAAE,CAAC,CAAC;MACpG,CAAC;IACH;IACA,OAAO,YAAY,CAAC,CAAC;EACvB,CAAC;EACDS,SAAS,EAAE,SAASA,SAASA,CAACT,kBAAkB,EAAE;IAChD,OAAOL,SAAS,CAACG,mBAAmB,CAACU,OAAO,CAACR,kBAAkB,CAAC,KAAK,CAAC,CAAC;EACzE,CAAC;EACDU,gBAAgB,EAAE,SAASA,gBAAgBA,CAACV,kBAAkB,EAAE;IAC9D,IAAIA,kBAAkB,KAAKW,QAAQ,EAAE;MACnC,IAAIC,iBAAiB,GAAGC,MAAM,CAACC,OAAO,KAAKpB,SAAS;MACpD,IAAIqB,YAAY,GAAG,CAACJ,QAAQ,CAACK,UAAU,IAAI,EAAE,MAAM,YAAY;MAC/D,OAAOJ,iBAAiB,GAAGC,MAAM,CAACI,OAAO,GAAGF,YAAY,GAAGJ,QAAQ,CAACO,eAAe,CAACC,UAAU,GAAGR,QAAQ,CAACS,IAAI,CAACD,UAAU;IAC3H,CAAC,MAAM;MACL,OAAOnB,kBAAkB,CAACmB,UAAU;IACtC;EACF,CAAC;EACDE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACrB,kBAAkB,EAAE;IAC9D,IAAIA,kBAAkB,KAAKW,QAAQ,EAAE;MACnC,IAAIC,iBAAiB,GAAGC,MAAM,CAACI,OAAO,KAAKvB,SAAS;MACpD,IAAIqB,YAAY,GAAG,CAACJ,QAAQ,CAACK,UAAU,IAAI,EAAE,MAAM,YAAY;MAC/D,OAAOJ,iBAAiB,GAAGC,MAAM,CAACC,OAAO,GAAGC,YAAY,GAAGJ,QAAQ,CAACO,eAAe,CAACI,SAAS,GAAGX,QAAQ,CAACS,IAAI,CAACE,SAAS;IACzH,CAAC,MAAM;MACL,OAAOtB,kBAAkB,CAACsB,SAAS;IACrC;EACF,CAAC;EACDnB,aAAa,EAAE,SAASA,aAAaA,CAACH,kBAAkB,EAAE;IACxD,IAAIuB,SAAS,GAAG5B,SAAS,CAACG,mBAAmB,CAACH,SAAS,CAACG,mBAAmB,CAACU,OAAO,CAACR,kBAAkB,CAAC,CAAC,CAACJ,YAAY,IAAI,EAAE;IAC3H2B,SAAS,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;MAC7B,OAAOA,CAAC,CAAC9B,SAAS,CAACe,gBAAgB,CAACV,kBAAkB,CAAC,EAAEL,SAAS,CAAC0B,gBAAgB,CAACrB,kBAAkB,CAAC,CAAC;IAC1G,CAAC,CAAC;EACJ,CAAC;EACD0B,eAAe,EAAE,SAASA,eAAeA,CAACC,OAAO,EAAE;IACjDhC,SAAS,CAACE,WAAW,CAACO,IAAI,CAACuB,OAAO,CAAC;EACrC,CAAC;EACDC,aAAa,EAAE,SAASA,aAAaA,CAACD,OAAO,EAAE3B,kBAAkB,EAAE;IACjE,IAAI6B,SAAS,GAAGlC,SAAS,CAACG,mBAAmB,CAACH,SAAS,CAACG,mBAAmB,CAACU,OAAO,CAACR,kBAAkB,CAAC,CAAC;IAExG,IAAI,CAAC6B,SAAS,CAACjC,YAAY,EAAE;MAC3BiC,SAAS,CAACjC,YAAY,GAAG,EAAE;IAC7B;IAEAiC,SAAS,CAACjC,YAAY,CAACQ,IAAI,CAACuB,OAAO,CAAC;EACtC,CAAC;EACDG,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpCnC,SAAS,CAACE,WAAW,CAAC2B,OAAO,CAAC,UAAUO,CAAC,EAAE;MACzC,OAAOA,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;EACDC,OAAO,EAAE,SAASA,OAAOA,CAACC,YAAY,EAAEC,UAAU,EAAE;IAClDvC,SAAS,CAACG,mBAAmB,CAAC0B,OAAO,CAAC,UAAUC,CAAC,EAAE;MACjD,OAAOA,CAAC,CAAC7B,YAAY,IAAI6B,CAAC,CAAC7B,YAAY,CAACH,MAAM,IAAIgC,CAAC,CAAC7B,YAAY,CAACY,OAAO,CAAC0B,UAAU,CAAC,GAAG,CAAC,CAAC,IAAIT,CAAC,CAAC7B,YAAY,CAACW,MAAM,CAACkB,CAAC,CAAC7B,YAAY,CAACY,OAAO,CAAC0B,UAAU,CAAC,EAAE,CAAC,CAAC;IAC3J,CAAC,CAAC;IAEF,IAAIvC,SAAS,CAACE,WAAW,IAAIF,SAAS,CAACE,WAAW,CAACJ,MAAM,IAAIE,SAAS,CAACE,WAAW,CAACW,OAAO,CAACyB,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7GtC,SAAS,CAACE,WAAW,CAACU,MAAM,CAACZ,SAAS,CAACE,WAAW,CAACW,OAAO,CAACyB,YAAY,CAAC,EAAE,CAAC,CAAC;IAC9E;IAEAtB,QAAQ,CAACwB,mBAAmB,CAAC,QAAQ,EAAExC,SAAS,CAACQ,aAAa,CAAC;EACjE,CAAC;EAGDiC,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAOzC,SAAS,CAACG,mBAAmB,CAAC0B,OAAO,CAAC,UAAUC,CAAC,EAAE;MACxD,OAAO9B,SAAS,CAACQ,aAAa,CAACsB,CAAC,CAAC;IACnC,CAAC,CAAC;EACJ;AACF,CAAC;AAED9C,OAAO,CAACS,OAAO,GAAGO,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}