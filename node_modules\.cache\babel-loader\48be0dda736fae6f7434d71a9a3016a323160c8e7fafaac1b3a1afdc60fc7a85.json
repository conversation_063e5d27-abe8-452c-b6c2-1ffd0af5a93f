{"ast": null, "code": "export class EmailJSResponseStatus {\n  constructor() {\n    let _status = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    let _text = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Network Error';\n    this.status = _status;\n    this.text = _text;\n  }\n}", "map": {"version": 3, "names": ["EmailJSResponseStatus", "constructor", "_status", "arguments", "length", "undefined", "_text", "status", "text"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/models/EmailJSResponseStatus.js"], "sourcesContent": ["export class EmailJSResponseStatus {\n    constructor(_status = 0, _text = 'Network Error') {\n        this.status = _status;\n        this.text = _text;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,CAAC;EAC/BC,WAAWA,CAAA,EAAuC;IAAA,IAAtCC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,eAAe;IAC5C,IAAI,CAACI,MAAM,GAAGL,OAAO;IACrB,IAAI,CAACM,IAAI,GAAGF,KAAK;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}