{"ast": null, "code": "import { store } from '../../store/store';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\n/**\n * EmailJS global SDK config\n * @param {object} options - the EmailJS global SDK config options\n * @param {string} origin - the non-default EmailJS origin\n */\nexport const init = function (options) {\n  let origin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'https://api.emailjs.com';\n  if (!options) return;\n  const opts = buildOptions(options);\n  store.publicKey = opts.publicKey;\n  store.blockHeadless = opts.blockHeadless;\n  store.storageProvider = opts.storageProvider;\n  store.blockList = opts.blockList;\n  store.limitRate = opts.limitRate;\n  store.origin = opts.origin || origin;\n};", "map": {"version": 3, "names": ["store", "buildOptions", "init", "options", "origin", "arguments", "length", "undefined", "opts", "public<PERSON>ey", "blockHeadless", "storageProvider", "blockList", "limitRate"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/methods/init/init.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\n/**\n * EmailJS global SDK config\n * @param {object} options - the EmailJS global SDK config options\n * @param {string} origin - the non-default EmailJS origin\n */\nexport const init = (options, origin = 'https://api.emailjs.com') => {\n    if (!options)\n        return;\n    const opts = buildOptions(options);\n    store.publicKey = opts.publicKey;\n    store.blockHeadless = opts.blockHeadless;\n    store.storageProvider = opts.storageProvider;\n    store.blockList = opts.blockList;\n    store.limitRate = opts.limitRate;\n    store.origin = opts.origin || origin;\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC,SAASC,YAAY,QAAQ,uCAAuC;AACpE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG,SAAAA,CAACC,OAAO,EAAyC;EAAA,IAAvCC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,yBAAyB;EAC5D,IAAI,CAACF,OAAO,EACR;EACJ,MAAMK,IAAI,GAAGP,YAAY,CAACE,OAAO,CAAC;EAClCH,KAAK,CAACS,SAAS,GAAGD,IAAI,CAACC,SAAS;EAChCT,KAAK,CAACU,aAAa,GAAGF,IAAI,CAACE,aAAa;EACxCV,KAAK,CAACW,eAAe,GAAGH,IAAI,CAACG,eAAe;EAC5CX,KAAK,CAACY,SAAS,GAAGJ,IAAI,CAACI,SAAS;EAChCZ,KAAK,CAACa,SAAS,GAAGL,IAAI,CAACK,SAAS;EAChCb,KAAK,CAACI,MAAM,GAAGI,IAAI,CAACJ,MAAM,IAAIA,MAAM;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}