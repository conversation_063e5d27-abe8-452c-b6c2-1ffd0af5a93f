{"ast": null, "code": "import { store } from '../../store/store';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\n/**\n * EmailJS global SDK config\n * @param {object} options - the EmailJS global SDK config options\n * @param {string} origin - the non-default EmailJS origin\n */\nexport const init = (options, origin = 'https://api.emailjs.com') => {\n  if (!options) return;\n  const opts = buildOptions(options);\n  store.publicKey = opts.publicKey;\n  store.blockHeadless = opts.blockHeadless;\n  store.storageProvider = opts.storageProvider;\n  store.blockList = opts.blockList;\n  store.limitRate = opts.limitRate;\n  store.origin = opts.origin || origin;\n};", "map": {"version": 3, "names": ["store", "buildOptions", "init", "options", "origin", "opts", "public<PERSON>ey", "blockHeadless", "storageProvider", "blockList", "limitRate"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/methods/init/init.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\n/**\n * EmailJS global SDK config\n * @param {object} options - the EmailJS global SDK config options\n * @param {string} origin - the non-default EmailJS origin\n */\nexport const init = (options, origin = 'https://api.emailjs.com') => {\n    if (!options)\n        return;\n    const opts = buildOptions(options);\n    store.publicKey = opts.publicKey;\n    store.blockHeadless = opts.blockHeadless;\n    store.storageProvider = opts.storageProvider;\n    store.blockList = opts.blockList;\n    store.limitRate = opts.limitRate;\n    store.origin = opts.origin || origin;\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC,SAASC,YAAY,QAAQ,uCAAuC;AACpE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAGA,CAACC,OAAO,EAAEC,MAAM,GAAG,yBAAyB,KAAK;EACjE,IAAI,CAACD,OAAO,EACR;EACJ,MAAME,IAAI,GAAGJ,YAAY,CAACE,OAAO,CAAC;EAClCH,KAAK,CAACM,SAAS,GAAGD,IAAI,CAACC,SAAS;EAChCN,KAAK,CAACO,aAAa,GAAGF,IAAI,CAACE,aAAa;EACxCP,KAAK,CAACQ,eAAe,GAAGH,IAAI,CAACG,eAAe;EAC5CR,KAAK,CAACS,SAAS,GAAGJ,IAAI,CAACI,SAAS;EAChCT,KAAK,CAACU,SAAS,GAAGL,IAAI,CAACK,SAAS;EAChCV,KAAK,CAACI,MAAM,GAAGC,IAAI,CAACD,MAAM,IAAIA,MAAM;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}