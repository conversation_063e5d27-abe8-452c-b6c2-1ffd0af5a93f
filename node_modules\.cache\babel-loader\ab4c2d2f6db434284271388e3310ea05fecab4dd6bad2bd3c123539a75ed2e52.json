{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\ContactForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport emailjs from '@emailjs/browser';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ContactForm() {\n  _s();\n  // Initialize EmailJS\n  useEffect(() => {\n    emailjs.init('xT5_SFGhfJZhSmwZ2'); // Your public key\n  }, []);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [submitStatus, setSubmitStatus] = useState('');\n\n  // Handler for input field changes\n  const handleInputChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prevFormData => ({\n      ...prevFormData,\n      [name]: value\n    }));\n  };\n  const onSubmit = async event => {\n    event.preventDefault();\n    setLoading(true);\n    setSubmitStatus('');\n    try {\n      // EmailJS configuration\n      const serviceID = 'service_l3kiohx';\n      const templateID = 'template_portfolio'; // Create this template in EmailJS with your provided HTML\n      const publicKey = 'xT5_SFGhfJZhSmwZ2';\n\n      // Template parameters for EmailJS - matching your template variables\n      const templateParams = {\n        name: formData.name,\n        // {{name}} in your template\n        message: formData.message,\n        // {{message}} in your template\n        time: new Date().toLocaleString(),\n        // {{time}} in your template\n        email: formData.email,\n        // Additional field for reference\n        subject: formData.subject // Additional field for reference\n      };\n      const result = await emailjs.send(serviceID, templateID, templateParams, publicKey);\n      if (result.status === 200) {\n        setFormData({\n          name: '',\n          email: '',\n          subject: '',\n          message: ''\n        });\n        setSubmitStatus('success');\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('EmailJS Error:', error);\n      setSubmitStatus('error');\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    id: \"contact-form\",\n    onSubmit: onSubmit,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row gx-3 gy-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"name\",\n            placeholder: \"Name *\",\n            className: \"form-control\",\n            type: \"text\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"email\",\n            placeholder: \"Email *\",\n            className: \"form-control\",\n            type: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"subject\",\n            placeholder: \"Subject *\",\n            className: \"form-control\",\n            type: \"text\",\n            value: formData.subject,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"message\",\n            placeholder: \"Your message\",\n            rows: 4,\n            className: \"form-control\",\n            value: formData.message,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"send\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `px-btn w-100 ${loading ? 'disabled' : ''}`,\n            type: \"submit\",\n            disabled: loading,\n            children: loading ? 'Sending...' : 'Send Message'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), submitStatus === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-success mt-3\",\n          role: \"alert\",\n          children: \"Message sent successfully! I'll get back to you soon.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), submitStatus === 'error' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-danger mt-3\",\n          role: \"alert\",\n          children: \"Failed to send message. Please try again or contact me directly.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s(ContactForm, \"zA2WD8GgXpsfL6ToN6o32zGTjsE=\");\n_c = ContactForm;\nvar _c;\n$RefreshReg$(_c, \"ContactForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "emailjs", "jsxDEV", "_jsxDEV", "ContactForm", "_s", "init", "loading", "setLoading", "formData", "setFormData", "name", "email", "subject", "message", "submitStatus", "setSubmitStatus", "handleInputChange", "event", "value", "target", "prevFormData", "onSubmit", "preventDefault", "serviceID", "templateID", "public<PERSON>ey", "templateParams", "time", "Date", "toLocaleString", "result", "send", "status", "error", "console", "id", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "type", "onChange", "required", "rows", "disabled", "role", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/ContactForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport emailjs from '@emailjs/browser';\n\nexport default function ContactForm() {\n  // Initialize EmailJS\n  useEffect(() => {\n    emailjs.init('xT5_SFGhfJZhSmwZ2'); // Your public key\n  }, []);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n  });\n  const [submitStatus, setSubmitStatus] = useState('');\n\n  // Handler for input field changes\n  const handleInputChange = event => {\n    const { name, value } = event.target;\n    setFormData(prevFormData => ({\n      ...prevFormData,\n      [name]: value,\n    }));\n  };\n\n  const onSubmit = async event => {\n    event.preventDefault();\n    setLoading(true);\n    setSubmitStatus('');\n\n    try {\n      // EmailJS configuration\n      const serviceID = 'service_l3kiohx';\n      const templateID = 'template_portfolio'; // Create this template in EmailJS with your provided HTML\n      const publicKey = 'xT5_SFGhfJZhSmwZ2';\n\n      // Template parameters for EmailJS - matching your template variables\n      const templateParams = {\n        name: formData.name,           // {{name}} in your template\n        message: formData.message,     // {{message}} in your template\n        time: new Date().toLocaleString(), // {{time}} in your template\n        email: formData.email,         // Additional field for reference\n        subject: formData.subject,     // Additional field for reference\n      };\n\n      const result = await emailjs.send(serviceID, templateID, templateParams, publicKey);\n\n      if (result.status === 200) {\n        setFormData({ name: '', email: '', subject: '', message: '' });\n        setSubmitStatus('success');\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('EmailJS Error:', error);\n      setSubmitStatus('error');\n      setLoading(false);\n    }\n  };\n  return (\n    <form id=\"contact-form\" onSubmit={onSubmit}>\n      <div className=\"row gx-3 gy-4\">\n        <div className=\"col-md-6\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Full Name</label>\n            <input\n              name=\"name\"\n              placeholder=\"Name *\"\n              className=\"form-control\"\n              type=\"text\"\n              value={formData.name}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-md-6\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Email</label>\n            <input\n              name=\"email\"\n              placeholder=\"Email *\"\n              className=\"form-control\"\n              type=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-12\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Subject</label>\n            <input\n              name=\"subject\"\n              placeholder=\"Subject *\"\n              className=\"form-control\"\n              type=\"text\"\n              value={formData.subject}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-md-12\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">message</label>\n            <textarea\n              name=\"message\"\n              placeholder=\"Your message\"\n              rows={4}\n              className=\"form-control\"\n              value={formData.message}\n              onChange={handleInputChange}\n            />\n          </div>\n        </div>\n        <div className=\"col-md-12\">\n          <div className=\"send\">\n            <button\n              className={`px-btn w-100 ${loading ? 'disabled' : ''}`}\n              type=\"submit\"\n              disabled={loading}\n            >\n              {loading ? 'Sending...' : 'Send Message'}\n            </button>\n          </div>\n          {submitStatus === 'success' && (\n            <div className=\"alert alert-success mt-3\" role=\"alert\">\n              Message sent successfully! I'll get back to you soon.\n            </div>\n          )}\n          {submitStatus === 'error' && (\n            <div className=\"alert alert-danger mt-3\" role=\"alert\">\n              Failed to send message. Please try again or contact me directly.\n            </div>\n          )}\n        </div>\n      </div>\n    </form>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,OAAO,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC;EACAL,SAAS,CAAC,MAAM;IACdC,OAAO,CAACK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAMkB,iBAAiB,GAAGC,KAAK,IAAI;IACjC,MAAM;MAAEP,IAAI;MAAEQ;IAAM,CAAC,GAAGD,KAAK,CAACE,MAAM;IACpCV,WAAW,CAACW,YAAY,KAAK;MAC3B,GAAGA,YAAY;MACf,CAACV,IAAI,GAAGQ;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAMJ,KAAK,IAAI;IAC9BA,KAAK,CAACK,cAAc,CAAC,CAAC;IACtBf,UAAU,CAAC,IAAI,CAAC;IAChBQ,eAAe,CAAC,EAAE,CAAC;IAEnB,IAAI;MACF;MACA,MAAMQ,SAAS,GAAG,iBAAiB;MACnC,MAAMC,UAAU,GAAG,oBAAoB,CAAC,CAAC;MACzC,MAAMC,SAAS,GAAG,mBAAmB;;MAErC;MACA,MAAMC,cAAc,GAAG;QACrBhB,IAAI,EAAEF,QAAQ,CAACE,IAAI;QAAY;QAC/BG,OAAO,EAAEL,QAAQ,CAACK,OAAO;QAAM;QAC/Bc,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QAAE;QACnClB,KAAK,EAAEH,QAAQ,CAACG,KAAK;QAAU;QAC/BC,OAAO,EAAEJ,QAAQ,CAACI,OAAO,CAAM;MACjC,CAAC;MAED,MAAMkB,MAAM,GAAG,MAAM9B,OAAO,CAAC+B,IAAI,CAACR,SAAS,EAAEC,UAAU,EAAEE,cAAc,EAAED,SAAS,CAAC;MAEnF,IAAIK,MAAM,CAACE,MAAM,KAAK,GAAG,EAAE;QACzBvB,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC,CAAC;QAC9DE,eAAe,CAAC,SAAS,CAAC;QAC1BR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtClB,eAAe,CAAC,OAAO,CAAC;MACxBR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,oBACEL,OAAA;IAAMiC,EAAE,EAAC,cAAc;IAACd,QAAQ,EAAEA,QAAS;IAAAe,QAAA,eACzClC,OAAA;MAAKmC,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC5BlC,OAAA;QAAKmC,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvBlC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBlC,OAAA;YAAOmC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/CvC,OAAA;YACEQ,IAAI,EAAC,MAAM;YACXgC,WAAW,EAAC,QAAQ;YACpBL,SAAS,EAAC,cAAc;YACxBM,IAAI,EAAC,MAAM;YACXzB,KAAK,EAAEV,QAAQ,CAACE,IAAK;YACrBkC,QAAQ,EAAE5B,iBAAkB;YAC5B6B,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvC,OAAA;QAAKmC,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvBlC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBlC,OAAA;YAAOmC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CvC,OAAA;YACEQ,IAAI,EAAC,OAAO;YACZgC,WAAW,EAAC,SAAS;YACrBL,SAAS,EAAC,cAAc;YACxBM,IAAI,EAAC,OAAO;YACZzB,KAAK,EAAEV,QAAQ,CAACG,KAAM;YACtBiC,QAAQ,EAAE5B,iBAAkB;YAC5B6B,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvC,OAAA;QAAKmC,SAAS,EAAC,QAAQ;QAAAD,QAAA,eACrBlC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBlC,OAAA;YAAOmC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CvC,OAAA;YACEQ,IAAI,EAAC,SAAS;YACdgC,WAAW,EAAC,WAAW;YACvBL,SAAS,EAAC,cAAc;YACxBM,IAAI,EAAC,MAAM;YACXzB,KAAK,EAAEV,QAAQ,CAACI,OAAQ;YACxBgC,QAAQ,EAAE5B,iBAAkB;YAC5B6B,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvC,OAAA;QAAKmC,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBlC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBlC,OAAA;YAAOmC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CvC,OAAA;YACEQ,IAAI,EAAC,SAAS;YACdgC,WAAW,EAAC,cAAc;YAC1BI,IAAI,EAAE,CAAE;YACRT,SAAS,EAAC,cAAc;YACxBnB,KAAK,EAAEV,QAAQ,CAACK,OAAQ;YACxB+B,QAAQ,EAAE5B;UAAkB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvC,OAAA;QAAKmC,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBlC,OAAA;UAAKmC,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBlC,OAAA;YACEmC,SAAS,EAAE,gBAAgB/B,OAAO,GAAG,UAAU,GAAG,EAAE,EAAG;YACvDqC,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAEzC,OAAQ;YAAA8B,QAAA,EAEjB9B,OAAO,GAAG,YAAY,GAAG;UAAc;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL3B,YAAY,KAAK,SAAS,iBACzBZ,OAAA;UAAKmC,SAAS,EAAC,0BAA0B;UAACW,IAAI,EAAC,OAAO;UAAAZ,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACA3B,YAAY,KAAK,OAAO,iBACvBZ,OAAA;UAAKmC,SAAS,EAAC,yBAAyB;UAACW,IAAI,EAAC,OAAO;UAAAZ,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAACrC,EAAA,CA1IuBD,WAAW;AAAA8C,EAAA,GAAX9C,WAAW;AAAA,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}