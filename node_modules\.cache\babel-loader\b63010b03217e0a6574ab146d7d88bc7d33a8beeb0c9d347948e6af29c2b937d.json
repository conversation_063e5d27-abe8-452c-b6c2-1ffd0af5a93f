{"ast": null, "code": "export const validateForm = form => {\n  if (!form || form.nodeName !== 'FORM') {\n    throw 'The 3rd parameter is expected to be the HTML form element or the style selector of the form';\n  }\n};", "map": {"version": 3, "names": ["validateForm", "form", "nodeName"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/validateForm/validateForm.js"], "sourcesContent": ["export const validateForm = (form) => {\n    if (!form || form.nodeName !== 'FORM') {\n        throw 'The 3rd parameter is expected to be the HTML form element or the style selector of the form';\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAIC,IAAI,IAAK;EAClC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,MAAM,6FAA6F;EACvG;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}