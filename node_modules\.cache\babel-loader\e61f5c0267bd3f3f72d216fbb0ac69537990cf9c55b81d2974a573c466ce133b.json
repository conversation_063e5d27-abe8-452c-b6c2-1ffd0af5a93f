{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Service.jsx\";\nimport { Icon } from '@iconify/react';\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Ratings from './Ratings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Service({\n  data\n}) {\n  console.log('Service data:', data);\n  if (!data) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Service: No data received\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 12\n    }, this);\n  }\n  const {\n    sectionHeading,\n    allService\n  } = data;\n  console.log('Service allService:', allService);\n  if (!allService) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Service: No allService data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section\",\n    id: \"services\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeading, {\n        miniTitle: sectionHeading === null || sectionHeading === void 0 ? void 0 : sectionHeading.miniTitle,\n        title: sectionHeading === null || sectionHeading === void 0 ? void 0 : sectionHeading.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row gy-5\",\n        children: allService === null || allService === void 0 ? void 0 : allService.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-sm-6 col-lg-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"services-box\",\n            style: {\n              backgroundImage: `url(${item.imgUrl})`\n            },\n            \"data-aos\": \"fade-left\",\n            \"data-aos-duration\": \"1200\",\n            \"data-aos-delay\": index * 100,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"services-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"icon\",\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  icon: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: item.subTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rating-wrap\",\n                children: /*#__PURE__*/_jsxDEV(Ratings, {\n                  ratings: item.ratings\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_c = Service;\nvar _c;\n$RefreshReg$(_c, \"Service\");", "map": {"version": 3, "names": ["Icon", "React", "SectionHeading", "Ratings", "jsxDEV", "_jsxDEV", "Service", "data", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sectionHeading", "allService", "className", "id", "miniTitle", "title", "map", "item", "index", "style", "backgroundImage", "imgUrl", "icon", "subTitle", "ratings", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Service.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Ratings from './Ratings';\n\nexport default function Service({ data }) {\n  console.log('Service data:', data);\n\n  if (!data) {\n    return <div>Service: No data received</div>;\n  }\n\n  const { sectionHeading, allService } = data;\n  console.log('Service allService:', allService);\n\n  if (!allService) {\n    return <div>Service: No allService data</div>;\n  }\n\n  return (\n    <section className=\"section\" id=\"services\">\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading?.miniTitle}\n          title={sectionHeading?.title}\n        />\n        <div className=\"row gy-5\">\n          {allService?.map((item, index) => (\n            <div className=\"col-sm-6 col-lg-3\" key={index}>\n              <div\n                className=\"services-box\"\n                style={{ backgroundImage: `url(${item.imgUrl})` }}\n                data-aos=\"fade-left\"\n                data-aos-duration=\"1200\"\n                data-aos-delay={index * 100}\n              >\n                <div className=\"services-body\">\n                  <div className=\"icon\">\n                    <Icon icon={item.icon} />\n                  </div>\n                  <h5>{item.title}</h5>\n                  <p>{item.subTitle}</p>\n                  <div className=\"rating-wrap\">\n                    <Ratings ratings={item.ratings} />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,OAAO,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,eAAe,SAASC,OAAOA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACxCC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,IAAI,CAAC;EAElC,IAAI,CAACA,IAAI,EAAE;IACT,oBAAOF,OAAA;MAAAK,QAAA,EAAK;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC7C;EAEA,MAAM;IAAEC,cAAc;IAAEC;EAAW,CAAC,GAAGT,IAAI;EAC3CC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,UAAU,CAAC;EAE9C,IAAI,CAACA,UAAU,EAAE;IACf,oBAAOX,OAAA;MAAAK,QAAA,EAAK;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC/C;EAEA,oBACET,OAAA;IAASY,SAAS,EAAC,SAAS;IAACC,EAAE,EAAC,UAAU;IAAAR,QAAA,eACxCL,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAP,QAAA,gBACxBL,OAAA,CAACH,cAAc;QACbiB,SAAS,EAAEJ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEI,SAAU;QACrCC,KAAK,EAAEL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEK;MAAM;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFT,OAAA;QAAKY,SAAS,EAAC,UAAU;QAAAP,QAAA,EACtBM,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BlB,OAAA;UAAKY,SAAS,EAAC,mBAAmB;UAAAP,QAAA,eAChCL,OAAA;YACEY,SAAS,EAAC,cAAc;YACxBO,KAAK,EAAE;cAAEC,eAAe,EAAE,OAAOH,IAAI,CAACI,MAAM;YAAI,CAAE;YAClD,YAAS,WAAW;YACpB,qBAAkB,MAAM;YACxB,kBAAgBH,KAAK,GAAG,GAAI;YAAAb,QAAA,eAE5BL,OAAA;cAAKY,SAAS,EAAC,eAAe;cAAAP,QAAA,gBAC5BL,OAAA;gBAAKY,SAAS,EAAC,MAAM;gBAAAP,QAAA,eACnBL,OAAA,CAACL,IAAI;kBAAC2B,IAAI,EAAEL,IAAI,CAACK;gBAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNT,OAAA;gBAAAK,QAAA,EAAKY,IAAI,CAACF;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBT,OAAA;gBAAAK,QAAA,EAAIY,IAAI,CAACM;cAAQ;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBT,OAAA;gBAAKY,SAAS,EAAC,aAAa;gBAAAP,QAAA,eAC1BL,OAAA,CAACF,OAAO;kBAAC0B,OAAO,EAAEP,IAAI,CAACO;gBAAQ;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAlBgCS,KAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBxC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACgB,EAAA,GAhDuBxB,OAAO;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}