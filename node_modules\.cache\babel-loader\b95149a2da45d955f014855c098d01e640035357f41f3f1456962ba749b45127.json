{"ast": null, "code": "export const createWebStorage = () => {\n  if (typeof localStorage === 'undefined') return;\n  return {\n    get: key => Promise.resolve(localStorage.getItem(key)),\n    set: (key, value) => Promise.resolve(localStorage.setItem(key, value)),\n    remove: key => Promise.resolve(localStorage.removeItem(key))\n  };\n};", "map": {"version": 3, "names": ["createWebStorage", "localStorage", "get", "key", "Promise", "resolve", "getItem", "set", "value", "setItem", "remove", "removeItem"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/createWebStorage/createWebStorage.js"], "sourcesContent": ["export const createWebStorage = () => {\n    if (typeof localStorage === 'undefined')\n        return;\n    return {\n        get: (key) => Promise.resolve(localStorage.getItem(key)),\n        set: (key, value) => Promise.resolve(localStorage.setItem(key, value)),\n        remove: (key) => Promise.resolve(localStorage.removeItem(key)),\n    };\n};\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,GAAGA,CAAA,KAAM;EAClC,IAAI,OAAOC,YAAY,KAAK,WAAW,EACnC;EACJ,OAAO;IACHC,GAAG,EAAGC,GAAG,IAAKC,OAAO,CAACC,OAAO,CAACJ,YAAY,CAACK,OAAO,CAACH,GAAG,CAAC,CAAC;IACxDI,GAAG,EAAEA,CAACJ,GAAG,EAAEK,KAAK,KAAKJ,OAAO,CAACC,OAAO,CAACJ,YAAY,CAACQ,OAAO,CAACN,GAAG,EAAEK,KAAK,CAAC,CAAC;IACtEE,MAAM,EAAGP,GAAG,IAAKC,OAAO,CAACC,OAAO,CAACJ,YAAY,CAACU,UAAU,CAACR,GAAG,CAAC;EACjE,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}