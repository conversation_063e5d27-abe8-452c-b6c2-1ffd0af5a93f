{"ast": null, "code": "import React from'react';import SectionHeading from'./SectionHeading';import Slider from'react-slick';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function Testimonial(_ref){let{data}=_ref;const{sectionHeading,allTestimonial}=data;var settings={dots:true,arrows:false,infinite:true,autoplay:false,autoplaySpeed:4000,speed:1000,slidesToShow:1,slidesToScroll:1,initialSlide:0};return/*#__PURE__*/_jsxs(\"section\",{className:\"section effect-section pb-0\",style:{display:'block',visibility:'visible',backgroundColor:'rgba(0,0,255,0.1)',minHeight:'200px'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"effect-3\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/images/effect-3.svg\",title:true,alt:\"\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"effect-4\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/images/effect-4.svg\",title:true,alt:\"\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(SectionHeading,{miniTitle:sectionHeading.miniTitle,title:sectionHeading.title,variant:\"text-center\"}),/*#__PURE__*/_jsx(\"div\",{\"data-aos\":\"fade\",\"data-aos-duration\":\"1200\",\"data-aos-delay\":\"300\",children:/*#__PURE__*/_jsx(Slider,{...settings,children:allTestimonial===null||allTestimonial===void 0?void 0:allTestimonial.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"testimonial-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"t-user\",children:/*#__PURE__*/_jsx(\"img\",{src:item.avatarImg,alt:\"Avatar\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"t-text\",children:item.reviewText}),/*#__PURE__*/_jsxs(\"div\",{className:\"t-person\",children:[/*#__PURE__*/_jsx(\"h6\",{children:item.avatarName}),/*#__PURE__*/_jsx(\"span\",{children:item.avatarCompany})]})]})},index))})})]})]});}", "map": {"version": 3, "names": ["React", "SectionHeading", "Slide<PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Testimonial", "_ref", "data", "sectionHeading", "allTestimonial", "settings", "dots", "arrows", "infinite", "autoplay", "autoplaySpeed", "speed", "slidesToShow", "slidesToScroll", "initialSlide", "className", "style", "display", "visibility", "backgroundColor", "minHeight", "children", "src", "title", "alt", "miniTitle", "variant", "map", "item", "index", "avatarImg", "reviewText", "avatar<PERSON><PERSON>", "avatarCompany"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Testimonial.jsx"], "sourcesContent": ["import React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\n\nexport default function Testimonial({ data }) {\n  const { sectionHeading, allTestimonial } = data;\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: false,\n    autoplaySpeed: 4000,\n    speed: 1000,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0,\n  };\n  return (\n    <section className=\"section effect-section pb-0\" style={{ display: 'block', visibility: 'visible', backgroundColor: 'rgba(0,0,255,0.1)', minHeight: '200px' }}>\n      <div className=\"effect-3\">\n        <img src=\"/images/effect-3.svg\" title alt=\"\" />\n      </div>\n      <div className=\"effect-4\">\n        <img src=\"/images/effect-4.svg\" title alt=\"\" />\n      </div>\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n          variant=\"text-center\"\n        />\n        <div data-aos=\"fade\" data-aos-duration=\"1200\" data-aos-delay=\"300\">\n          <Slider {...settings}>\n            {allTestimonial?.map((item, index) => (\n              <div key={index}>\n                <div className=\"testimonial-box\">\n                  <div className=\"t-user\">\n                    <img src={item.avatarImg} alt=\"Avatar\" />\n                  </div>\n                  <div className=\"t-text\">{item.reviewText}</div>\n                  <div className=\"t-person\">\n                    <h6>{item.avatarName}</h6>\n                    <span>{item.avatarCompany}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </Slider>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,MAAM,KAAM,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjC,cAAe,SAAS,CAAAC,WAAWA,CAAAC,IAAA,CAAW,IAAV,CAAEC,IAAK,CAAC,CAAAD,IAAA,CAC1C,KAAM,CAAEE,cAAc,CAAEC,cAAe,CAAC,CAAGF,IAAI,CAC/C,GAAI,CAAAG,QAAQ,CAAG,CACbC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,KAAK,CACbC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,IAAI,CACnBC,KAAK,CAAE,IAAI,CACXC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAE,CAChB,CAAC,CACD,mBACEf,KAAA,YAASgB,SAAS,CAAC,6BAA6B,CAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEC,UAAU,CAAE,SAAS,CAAEC,eAAe,CAAE,mBAAmB,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAC,QAAA,eAC5JxB,IAAA,QAAKkB,SAAS,CAAC,UAAU,CAAAM,QAAA,cACvBxB,IAAA,QAAKyB,GAAG,CAAC,sBAAsB,CAACC,KAAK,MAACC,GAAG,CAAC,EAAE,CAAE,CAAC,CAC5C,CAAC,cACN3B,IAAA,QAAKkB,SAAS,CAAC,UAAU,CAAAM,QAAA,cACvBxB,IAAA,QAAKyB,GAAG,CAAC,sBAAsB,CAACC,KAAK,MAACC,GAAG,CAAC,EAAE,CAAE,CAAC,CAC5C,CAAC,cACNzB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBxB,IAAA,CAACH,cAAc,EACb+B,SAAS,CAAEtB,cAAc,CAACsB,SAAU,CACpCF,KAAK,CAAEpB,cAAc,CAACoB,KAAM,CAC5BG,OAAO,CAAC,aAAa,CACtB,CAAC,cACF7B,IAAA,QAAK,WAAS,MAAM,CAAC,oBAAkB,MAAM,CAAC,iBAAe,KAAK,CAAAwB,QAAA,cAChExB,IAAA,CAACF,MAAM,KAAKU,QAAQ,CAAAgB,QAAA,CACjBjB,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEuB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC/BhC,IAAA,QAAAwB,QAAA,cACEtB,KAAA,QAAKgB,SAAS,CAAC,iBAAiB,CAAAM,QAAA,eAC9BxB,IAAA,QAAKkB,SAAS,CAAC,QAAQ,CAAAM,QAAA,cACrBxB,IAAA,QAAKyB,GAAG,CAAEM,IAAI,CAACE,SAAU,CAACN,GAAG,CAAC,QAAQ,CAAE,CAAC,CACtC,CAAC,cACN3B,IAAA,QAAKkB,SAAS,CAAC,QAAQ,CAAAM,QAAA,CAAEO,IAAI,CAACG,UAAU,CAAM,CAAC,cAC/ChC,KAAA,QAAKgB,SAAS,CAAC,UAAU,CAAAM,QAAA,eACvBxB,IAAA,OAAAwB,QAAA,CAAKO,IAAI,CAACI,UAAU,CAAK,CAAC,cAC1BnC,IAAA,SAAAwB,QAAA,CAAOO,IAAI,CAACK,aAAa,CAAO,CAAC,EAC9B,CAAC,EACH,CAAC,EAVEJ,KAWL,CACN,CAAC,CACI,CAAC,CACN,CAAC,EACH,CAAC,EACC,CAAC,CAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}