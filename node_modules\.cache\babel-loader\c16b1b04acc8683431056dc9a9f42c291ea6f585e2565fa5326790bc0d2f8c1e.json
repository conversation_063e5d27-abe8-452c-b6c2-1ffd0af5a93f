{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function ContactInfo(_ref){let{contactInfoData}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:\"contact-info\",children:contactInfoData.map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"contact-info-in\",children:[/*#__PURE__*/_jsx(\"label\",{children:item.title}),item.email&&/*#__PURE__*/_jsx(\"a\",{href:`mailto:${item.email}`,children:item.email}),item.tel&&/*#__PURE__*/_jsx(\"a\",{href:`tel:${item.tel}`,children:item.tel})]},index))});}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ContactInfo", "_ref", "contactInfoData", "className", "children", "map", "item", "index", "title", "email", "href", "tel"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/ContactInfo.jsx"], "sourcesContent": ["import React from 'react';\n\nexport default function ContactInfo({ contactInfoData }) {\n  return (\n    <div className=\"contact-info\">\n      {contactInfoData.map((item, index) => (\n        <div\n          className=\"contact-info-in\"\n          key={index}\n        >\n          <label>{item.title}</label>\n          {item.email && <a href={`mailto:${item.email}`}>{item.email}</a>}\n          {item.tel && <a href={`tel:${item.tel}`}>{item.tel}</a>}\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,cAAe,SAAS,CAAAC,WAAWA,CAAAC,IAAA,CAAsB,IAArB,CAAEC,eAAgB,CAAC,CAAAD,IAAA,CACrD,mBACEJ,IAAA,QAAKM,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1BF,eAAe,CAACG,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC/BR,KAAA,QACEI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAG3BP,IAAA,UAAAO,QAAA,CAAQE,IAAI,CAACE,KAAK,CAAQ,CAAC,CAC1BF,IAAI,CAACG,KAAK,eAAIZ,IAAA,MAAGa,IAAI,CAAE,UAAUJ,IAAI,CAACG,KAAK,EAAG,CAAAL,QAAA,CAAEE,IAAI,CAACG,KAAK,CAAI,CAAC,CAC/DH,IAAI,CAACK,GAAG,eAAId,IAAA,MAAGa,IAAI,CAAE,OAAOJ,IAAI,CAACK,GAAG,EAAG,CAAAP,QAAA,CAAEE,IAAI,CAACK,GAAG,CAAI,CAAC,GAJlDJ,KAKF,CACN,CAAC,CACC,CAAC,CAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}