{"ast": null, "code": "import{Icon}from'@iconify/react';import React from'react';import SectionHeading from'./SectionHeading';import Ratings from'./Ratings';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function Service(_ref){let{data}=_ref;const{sectionHeading,allService}=data;return/*#__PURE__*/_jsx(\"section\",{className:\"section\",id:\"services\",style:{display:'block',visibility:'visible',backgroundColor:'rgba(255,0,0,0.1)',minHeight:'200px'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(SectionHeading,{miniTitle:sectionHeading.miniTitle,title:sectionHeading.title}),/*#__PURE__*/_jsx(\"div\",{className:\"row gy-5\",children:allService===null||allService===void 0?void 0:allService.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"col-sm-6 col-lg-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"services-box\",style:{backgroundImage:`url(${item.imgUrl})`},\"data-aos\":\"fade-left\",\"data-aos-duration\":\"1200\",\"data-aos-delay\":index*100,children:/*#__PURE__*/_jsxs(\"div\",{className:\"services-body\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"icon\",children:/*#__PURE__*/_jsx(Icon,{icon:item.icon})}),/*#__PURE__*/_jsx(\"h5\",{children:item.title}),/*#__PURE__*/_jsx(\"p\",{children:item.subTitle}),/*#__PURE__*/_jsx(\"div\",{className:\"rating-wrap\",children:/*#__PURE__*/_jsx(Ratings,{ratings:item.ratings})})]})})},index))})]})});}", "map": {"version": 3, "names": ["Icon", "React", "SectionHeading", "Ratings", "jsx", "_jsx", "jsxs", "_jsxs", "Service", "_ref", "data", "sectionHeading", "allService", "className", "id", "style", "display", "visibility", "backgroundColor", "minHeight", "children", "miniTitle", "title", "map", "item", "index", "backgroundImage", "imgUrl", "icon", "subTitle", "ratings"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Service.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Ratings from './Ratings';\n\nexport default function Service({ data }) {\n  const { sectionHeading, allService } = data;\n  return (\n    <section className=\"section\" id=\"services\" style={{ display: 'block', visibility: 'visible', backgroundColor: 'rgba(255,0,0,0.1)', minHeight: '200px' }}>\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n        />\n        <div className=\"row gy-5\">\n          {allService?.map((item, index) => (\n            <div className=\"col-sm-6 col-lg-3\" key={index}>\n              <div\n                className=\"services-box\"\n                style={{ backgroundImage: `url(${item.imgUrl})` }}\n                data-aos=\"fade-left\"\n                data-aos-duration=\"1200\"\n                data-aos-delay={index * 100}\n              >\n                <div className=\"services-body\">\n                  <div className=\"icon\">\n                    <Icon icon={item.icon} />\n                  </div>\n                  <h5>{item.title}</h5>\n                  <p>{item.subTitle}</p>\n                  <div className=\"rating-wrap\">\n                    <Ratings ratings={item.ratings} />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": "AAAA,OAASA,IAAI,KAAQ,gBAAgB,CACrC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,OAAO,KAAM,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhC,cAAe,SAAS,CAAAC,OAAOA,CAAAC,IAAA,CAAW,IAAV,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACtC,KAAM,CAAEE,cAAc,CAAEC,UAAW,CAAC,CAAGF,IAAI,CAC3C,mBACEL,IAAA,YAASQ,SAAS,CAAC,SAAS,CAACC,EAAE,CAAC,UAAU,CAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEC,UAAU,CAAE,SAAS,CAAEC,eAAe,CAAE,mBAAmB,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAC,QAAA,cACtJb,KAAA,QAAKM,SAAS,CAAC,WAAW,CAAAO,QAAA,eACxBf,IAAA,CAACH,cAAc,EACbmB,SAAS,CAAEV,cAAc,CAACU,SAAU,CACpCC,KAAK,CAAEX,cAAc,CAACW,KAAM,CAC7B,CAAC,cACFjB,IAAA,QAAKQ,SAAS,CAAC,UAAU,CAAAO,QAAA,CACtBR,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEW,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC3BpB,IAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAO,QAAA,cAChCf,IAAA,QACEQ,SAAS,CAAC,cAAc,CACxBE,KAAK,CAAE,CAAEW,eAAe,CAAE,OAAOF,IAAI,CAACG,MAAM,GAAI,CAAE,CAClD,WAAS,WAAW,CACpB,oBAAkB,MAAM,CACxB,iBAAgBF,KAAK,CAAG,GAAI,CAAAL,QAAA,cAE5Bb,KAAA,QAAKM,SAAS,CAAC,eAAe,CAAAO,QAAA,eAC5Bf,IAAA,QAAKQ,SAAS,CAAC,MAAM,CAAAO,QAAA,cACnBf,IAAA,CAACL,IAAI,EAAC4B,IAAI,CAAEJ,IAAI,CAACI,IAAK,CAAE,CAAC,CACtB,CAAC,cACNvB,IAAA,OAAAe,QAAA,CAAKI,IAAI,CAACF,KAAK,CAAK,CAAC,cACrBjB,IAAA,MAAAe,QAAA,CAAII,IAAI,CAACK,QAAQ,CAAI,CAAC,cACtBxB,IAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAO,QAAA,cAC1Bf,IAAA,CAACF,OAAO,EAAC2B,OAAO,CAAEN,IAAI,CAACM,OAAQ,CAAE,CAAC,CAC/B,CAAC,EACH,CAAC,CACH,CAAC,EAlBgCL,KAmBnC,CACN,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}