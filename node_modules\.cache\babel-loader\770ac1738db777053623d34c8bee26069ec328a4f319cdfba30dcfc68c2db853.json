{"ast": null, "code": "export const buildOptions = options => {\n  if (!options) return {};\n  // support compatibility with SDK v3\n  if (typeof options === 'string') {\n    return {\n      publicKey: options\n    };\n  }\n  // eslint-disable-next-line @typescript-eslint/no-base-to-string\n  if (options.toString() === '[object Object]') {\n    return options;\n  }\n  return {};\n};", "map": {"version": 3, "names": ["buildOptions", "options", "public<PERSON>ey", "toString"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/buildOptions/buildOptions.js"], "sourcesContent": ["export const buildOptions = (options) => {\n    if (!options)\n        return {};\n    // support compatibility with SDK v3\n    if (typeof options === 'string') {\n        return {\n            publicKey: options,\n        };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    if (options.toString() === '[object Object]') {\n        return options;\n    }\n    return {};\n};\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAIC,OAAO,IAAK;EACrC,IAAI,CAACA,OAAO,EACR,OAAO,CAAC,CAAC;EACb;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC7B,OAAO;MACHC,SAAS,EAAED;IACf,CAAC;EACL;EACA;EACA,IAAIA,OAAO,CAACE,QAAQ,CAAC,CAAC,KAAK,iBAAiB,EAAE;IAC1C,OAAOF,OAAO;EAClB;EACA,OAAO,CAAC,CAAC;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}