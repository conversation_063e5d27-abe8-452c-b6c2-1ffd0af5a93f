{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\ContactForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport emailjs from '@emailjs/browser';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ContactForm() {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [submitStatus, setSubmitStatus] = useState('');\n\n  // Handler for input field changes\n  const handleInputChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prevFormData => ({\n      ...prevFormData,\n      [name]: value\n    }));\n  };\n  const onSubmit = async event => {\n    event.preventDefault();\n    setLoading(true);\n    setSubmitStatus('');\n    try {\n      // EmailJS configuration\n      const serviceID = 'service_l3kiohx';\n      const templateID = 'template_portfolio'; // You'll need to create this template in EmailJS\n      const publicKey = 'xT5_SFGhfJZhSmwZ2';\n\n      // Template parameters for EmailJS\n      const templateParams = {\n        from_name: formData.name,\n        from_email: formData.email,\n        subject: formData.subject,\n        message: formData.message,\n        to_email: '<EMAIL>' // Your email where you want to receive messages\n      };\n      const result = await emailjs.send(serviceID, templateID, templateParams, publicKey);\n      if (result.status === 200) {\n        setFormData({\n          name: '',\n          email: '',\n          subject: '',\n          message: ''\n        });\n        setSubmitStatus('success');\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('EmailJS Error:', error);\n      setSubmitStatus('error');\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    id: \"contact-form\",\n    onSubmit: onSubmit,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row gx-3 gy-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"name\",\n            placeholder: \"Name *\",\n            className: \"form-control\",\n            type: \"text\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"email\",\n            placeholder: \"Email *\",\n            className: \"form-control\",\n            type: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"subject\",\n            placeholder: \"Subject *\",\n            className: \"form-control\",\n            type: \"text\",\n            value: formData.subject,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"message\",\n            placeholder: \"Your message\",\n            rows: 4,\n            className: \"form-control\",\n            value: formData.message,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"send\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `px-btn w-100 ${loading ? 'disabled' : ''}`,\n            type: \"submit\",\n            disabled: loading,\n            children: loading ? 'Sending...' : 'Send Message'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), submitStatus === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-success mt-3\",\n          role: \"alert\",\n          children: \"Message sent successfully! I'll get back to you soon.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), submitStatus === 'error' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-danger mt-3\",\n          role: \"alert\",\n          children: \"Failed to send message. Please try again or contact me directly.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_s(ContactForm, \"01OcO8Bt/0G/JsTq6zJ1JAJY3Vo=\");\n_c = ContactForm;\nvar _c;\n$RefreshReg$(_c, \"ContactForm\");", "map": {"version": 3, "names": ["React", "useState", "emailjs", "jsxDEV", "_jsxDEV", "ContactForm", "_s", "loading", "setLoading", "formData", "setFormData", "name", "email", "subject", "message", "submitStatus", "setSubmitStatus", "handleInputChange", "event", "value", "target", "prevFormData", "onSubmit", "preventDefault", "serviceID", "templateID", "public<PERSON>ey", "templateParams", "from_name", "from_email", "to_email", "result", "send", "status", "error", "console", "id", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "type", "onChange", "required", "rows", "disabled", "role", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/ContactForm.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport emailjs from '@emailjs/browser';\n\nexport default function ContactForm() {\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n  });\n  const [submitStatus, setSubmitStatus] = useState('');\n\n  // Handler for input field changes\n  const handleInputChange = event => {\n    const { name, value } = event.target;\n    setFormData(prevFormData => ({\n      ...prevFormData,\n      [name]: value,\n    }));\n  };\n\n  const onSubmit = async event => {\n    event.preventDefault();\n    setLoading(true);\n    setSubmitStatus('');\n\n    try {\n      // EmailJS configuration\n      const serviceID = 'service_l3kiohx';\n      const templateID = 'template_portfolio'; // You'll need to create this template in EmailJS\n      const publicKey = 'xT5_SFGhfJZhSmwZ2';\n\n      // Template parameters for EmailJS\n      const templateParams = {\n        from_name: formData.name,\n        from_email: formData.email,\n        subject: formData.subject,\n        message: formData.message,\n        to_email: '<EMAIL>', // Your email where you want to receive messages\n      };\n\n      const result = await emailjs.send(serviceID, templateID, templateParams, publicKey);\n\n      if (result.status === 200) {\n        setFormData({ name: '', email: '', subject: '', message: '' });\n        setSubmitStatus('success');\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('EmailJS Error:', error);\n      setSubmitStatus('error');\n      setLoading(false);\n    }\n  };\n  return (\n    <form id=\"contact-form\" onSubmit={onSubmit}>\n      <div className=\"row gx-3 gy-4\">\n        <div className=\"col-md-6\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Full Name</label>\n            <input\n              name=\"name\"\n              placeholder=\"Name *\"\n              className=\"form-control\"\n              type=\"text\"\n              value={formData.name}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-md-6\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Email</label>\n            <input\n              name=\"email\"\n              placeholder=\"Email *\"\n              className=\"form-control\"\n              type=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-12\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Subject</label>\n            <input\n              name=\"subject\"\n              placeholder=\"Subject *\"\n              className=\"form-control\"\n              type=\"text\"\n              value={formData.subject}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-md-12\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">message</label>\n            <textarea\n              name=\"message\"\n              placeholder=\"Your message\"\n              rows={4}\n              className=\"form-control\"\n              value={formData.message}\n              onChange={handleInputChange}\n            />\n          </div>\n        </div>\n        <div className=\"col-md-12\">\n          <div className=\"send\">\n            <button\n              className={`px-btn w-100 ${loading ? 'disabled' : ''}`}\n              type=\"submit\"\n              disabled={loading}\n            >\n              {loading ? 'Sending...' : 'Send Message'}\n            </button>\n          </div>\n          {submitStatus === 'success' && (\n            <div className=\"alert alert-success mt-3\" role=\"alert\">\n              Message sent successfully! I'll get back to you soon.\n            </div>\n          )}\n          {submitStatus === 'error' && (\n            <div className=\"alert alert-danger mt-3\" role=\"alert\">\n              Failed to send message. Please try again or contact me directly.\n            </div>\n          )}\n        </div>\n      </div>\n    </form>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,OAAO,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAMgB,iBAAiB,GAAGC,KAAK,IAAI;IACjC,MAAM;MAAEP,IAAI;MAAEQ;IAAM,CAAC,GAAGD,KAAK,CAACE,MAAM;IACpCV,WAAW,CAACW,YAAY,KAAK;MAC3B,GAAGA,YAAY;MACf,CAACV,IAAI,GAAGQ;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAMJ,KAAK,IAAI;IAC9BA,KAAK,CAACK,cAAc,CAAC,CAAC;IACtBf,UAAU,CAAC,IAAI,CAAC;IAChBQ,eAAe,CAAC,EAAE,CAAC;IAEnB,IAAI;MACF;MACA,MAAMQ,SAAS,GAAG,iBAAiB;MACnC,MAAMC,UAAU,GAAG,oBAAoB,CAAC,CAAC;MACzC,MAAMC,SAAS,GAAG,mBAAmB;;MAErC;MACA,MAAMC,cAAc,GAAG;QACrBC,SAAS,EAAEnB,QAAQ,CAACE,IAAI;QACxBkB,UAAU,EAAEpB,QAAQ,CAACG,KAAK;QAC1BC,OAAO,EAAEJ,QAAQ,CAACI,OAAO;QACzBC,OAAO,EAAEL,QAAQ,CAACK,OAAO;QACzBgB,QAAQ,EAAE,yBAAyB,CAAE;MACvC,CAAC;MAED,MAAMC,MAAM,GAAG,MAAM7B,OAAO,CAAC8B,IAAI,CAACR,SAAS,EAAEC,UAAU,EAAEE,cAAc,EAAED,SAAS,CAAC;MAEnF,IAAIK,MAAM,CAACE,MAAM,KAAK,GAAG,EAAE;QACzBvB,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC,CAAC;QAC9DE,eAAe,CAAC,SAAS,CAAC;QAC1BR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtClB,eAAe,CAAC,OAAO,CAAC;MACxBR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,oBACEJ,OAAA;IAAMgC,EAAE,EAAC,cAAc;IAACd,QAAQ,EAAEA,QAAS;IAAAe,QAAA,eACzCjC,OAAA;MAAKkC,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC5BjC,OAAA;QAAKkC,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvBjC,OAAA;UAAKkC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBjC,OAAA;YAAOkC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/CtC,OAAA;YACEO,IAAI,EAAC,MAAM;YACXgC,WAAW,EAAC,QAAQ;YACpBL,SAAS,EAAC,cAAc;YACxBM,IAAI,EAAC,MAAM;YACXzB,KAAK,EAAEV,QAAQ,CAACE,IAAK;YACrBkC,QAAQ,EAAE5B,iBAAkB;YAC5B6B,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtC,OAAA;QAAKkC,SAAS,EAAC,UAAU;QAAAD,QAAA,eACvBjC,OAAA;UAAKkC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBjC,OAAA;YAAOkC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CtC,OAAA;YACEO,IAAI,EAAC,OAAO;YACZgC,WAAW,EAAC,SAAS;YACrBL,SAAS,EAAC,cAAc;YACxBM,IAAI,EAAC,OAAO;YACZzB,KAAK,EAAEV,QAAQ,CAACG,KAAM;YACtBiC,QAAQ,EAAE5B,iBAAkB;YAC5B6B,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtC,OAAA;QAAKkC,SAAS,EAAC,QAAQ;QAAAD,QAAA,eACrBjC,OAAA;UAAKkC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBjC,OAAA;YAAOkC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CtC,OAAA;YACEO,IAAI,EAAC,SAAS;YACdgC,WAAW,EAAC,WAAW;YACvBL,SAAS,EAAC,cAAc;YACxBM,IAAI,EAAC,MAAM;YACXzB,KAAK,EAAEV,QAAQ,CAACI,OAAQ;YACxBgC,QAAQ,EAAE5B,iBAAkB;YAC5B6B,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtC,OAAA;QAAKkC,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBjC,OAAA;UAAKkC,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBjC,OAAA;YAAOkC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CtC,OAAA;YACEO,IAAI,EAAC,SAAS;YACdgC,WAAW,EAAC,cAAc;YAC1BI,IAAI,EAAE,CAAE;YACRT,SAAS,EAAC,cAAc;YACxBnB,KAAK,EAAEV,QAAQ,CAACK,OAAQ;YACxB+B,QAAQ,EAAE5B;UAAkB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtC,OAAA;QAAKkC,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBjC,OAAA;UAAKkC,SAAS,EAAC,MAAM;UAAAD,QAAA,eACnBjC,OAAA;YACEkC,SAAS,EAAE,gBAAgB/B,OAAO,GAAG,UAAU,GAAG,EAAE,EAAG;YACvDqC,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAEzC,OAAQ;YAAA8B,QAAA,EAEjB9B,OAAO,GAAG,YAAY,GAAG;UAAc;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL3B,YAAY,KAAK,SAAS,iBACzBX,OAAA;UAAKkC,SAAS,EAAC,0BAA0B;UAACW,IAAI,EAAC,OAAO;UAAAZ,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACA3B,YAAY,KAAK,OAAO,iBACvBX,OAAA;UAAKkC,SAAS,EAAC,yBAAyB;UAACW,IAAI,EAAC,OAAO;UAAAZ,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAACpC,EAAA,CAtIuBD,WAAW;AAAA6C,EAAA,GAAX7C,WAAW;AAAA,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}