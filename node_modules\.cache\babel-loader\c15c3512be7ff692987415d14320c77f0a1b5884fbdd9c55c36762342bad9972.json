{"ast": null, "code": "import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const headlessError = () => {\n  return new EmailJSResponseStatus(451, 'Unavailable For Headless Browser');\n};", "map": {"version": 3, "names": ["EmailJSResponseStatus", "headlessError"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/errors/headlessError/headlessError.js"], "sourcesContent": ["import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const headlessError = () => {\n    return new EmailJSResponseStatus(451, 'Unavailable For Headless Browser');\n};\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,oCAAoC;AAC1E,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC/B,OAAO,IAAID,qBAAqB,CAAC,GAAG,EAAE,kCAAkC,CAAC;AAC7E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}