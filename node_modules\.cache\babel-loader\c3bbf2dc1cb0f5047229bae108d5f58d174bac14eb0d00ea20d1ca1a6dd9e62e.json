{"ast": null, "code": "import { createWebStorage } from '../utils/createWebStorage/createWebStorage';\nexport const store = {\n  origin: 'https://api.emailjs.com',\n  blockHeadless: false,\n  storageProvider: createWebStorage()\n};", "map": {"version": 3, "names": ["createWebStorage", "store", "origin", "blockHeadless", "storageProvider"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/store/store.js"], "sourcesContent": ["import { createWebStorage } from '../utils/createWebStorage/createWebStorage';\nexport const store = {\n    origin: 'https://api.emailjs.com',\n    blockHeadless: false,\n    storageProvider: createWebStorage(),\n};\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,4CAA4C;AAC7E,OAAO,MAAMC,KAAK,GAAG;EACjBC,MAAM,EAAE,yBAAyB;EACjCC,aAAa,EAAE,KAAK;EACpBC,eAAe,EAAEJ,gBAAgB,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}