{"ast": null, "code": "export const validateLimitRateParams = (throttle, id) => {\n  if (typeof throttle !== 'number' || throttle < 0) {\n    throw 'The LimitRate throttle has to be a positive number';\n  }\n  if (id && typeof id !== 'string') {\n    throw 'The LimitRate ID has to be a non-empty string';\n  }\n};", "map": {"version": 3, "names": ["validateLimitRateParams", "throttle", "id"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/validateLimitRateParams/validateLimitRateParams.js"], "sourcesContent": ["export const validateLimitRateParams = (throttle, id) => {\n    if (typeof throttle !== 'number' || throttle < 0) {\n        throw 'The LimitRate throttle has to be a positive number';\n    }\n    if (id && typeof id !== 'string') {\n        throw 'The LimitRate ID has to be a non-empty string';\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,uBAAuB,GAAGA,CAACC,QAAQ,EAAEC,EAAE,KAAK;EACrD,IAAI,OAAOD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAC9C,MAAM,oDAAoD;EAC9D;EACA,IAAIC,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;IAC9B,MAAM,+CAA+C;EACzD;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}