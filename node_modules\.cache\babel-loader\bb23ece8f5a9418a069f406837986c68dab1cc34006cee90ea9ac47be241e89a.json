{"ast": null, "code": "import{Icon}from'@iconify/react';import React from'react';import{Link}from'react-router-dom';import{jsx as _jsx}from\"react/jsx-runtime\";export default function SocialBtns(_ref){let{variant,socialBtns}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:`social-icon ${variant?variant:''}`,children:socialBtns===null||socialBtns===void 0?void 0:socialBtns.map((item,index)=>/*#__PURE__*/_jsx(Link,{className:item.iconBgClass,to:item.href,target:\"_blank\",children:/*#__PURE__*/_jsx(Icon,{icon:item.icon})},index))});}", "map": {"version": 3, "names": ["Icon", "React", "Link", "jsx", "_jsx", "SocialBtns", "_ref", "variant", "socialBtns", "className", "children", "map", "item", "index", "iconBgClass", "to", "href", "target", "icon"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/SocialBtns.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React from 'react';\nimport { Link } from 'react-router-dom';\n\nexport default function SocialBtns({ variant, socialBtns }) {\n  return (\n    <div\n      className={`social-icon ${variant ? variant : ''}`}\n    >\n      {socialBtns?.map((item, index) => (\n        <Link\n          className={item.iconBgClass}\n          to={item.href}\n          key={index}\n          target=\"_blank\"\n        >\n          <Icon icon={item.icon} />\n        </Link>\n      ))}\n    </div>\n  );\n}\n"], "mappings": "AAAA,OAASA,IAAI,KAAQ,gBAAgB,CACrC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAExC,cAAe,SAAS,CAAAC,UAAUA,CAAAC,IAAA,CAA0B,IAAzB,CAAEC,OAAO,CAAEC,UAAW,CAAC,CAAAF,IAAA,CACxD,mBACEF,IAAA,QACEK,SAAS,CAAE,eAAeF,OAAO,CAAGA,OAAO,CAAG,EAAE,EAAG,CAAAG,QAAA,CAElDF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEG,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC3BT,IAAA,CAACF,IAAI,EACHO,SAAS,CAAEG,IAAI,CAACE,WAAY,CAC5BC,EAAE,CAAEH,IAAI,CAACI,IAAK,CAEdC,MAAM,CAAC,QAAQ,CAAAP,QAAA,cAEfN,IAAA,CAACJ,IAAI,EAACkB,IAAI,CAAEN,IAAI,CAACM,IAAK,CAAE,CAAC,EAHpBL,KAID,CACP,CAAC,CACC,CAAC,CAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}