{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Testimonial.jsx\";\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Testimonial({\n  data\n}) {\n  const {\n    sectionHeading,\n    allTestimonial\n  } = data;\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: false,\n    autoplaySpeed: 4000,\n    speed: 1000,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section effect-section pb-0\",\n    style: {\n      display: 'block',\n      visibility: 'visible',\n      backgroundColor: 'rgba(0,0,255,0.1)',\n      minHeight: '200px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"effect-3\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/effect-3.svg\",\n        title: true,\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"effect-4\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/effect-4.svg\",\n        title: true,\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeading, {\n        miniTitle: sectionHeading.miniTitle,\n        title: sectionHeading.title,\n        variant: \"text-center\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        \"data-aos\": \"fade\",\n        \"data-aos-duration\": \"1200\",\n        \"data-aos-delay\": \"300\",\n        children: /*#__PURE__*/_jsxDEV(Slider, {\n          ...settings,\n          children: allTestimonial === null || allTestimonial === void 0 ? void 0 : allTestimonial.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"t-user\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: item.avatarImg,\n                  alt: \"Avatar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 38,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"t-text\",\n                children: item.reviewText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"t-person\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: item.avatarName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: item.avatarCompany\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = Testimonial;\nvar _c;\n$RefreshReg$(_c, \"Testimonial\");", "map": {"version": 3, "names": ["React", "SectionHeading", "Slide<PERSON>", "jsxDEV", "_jsxDEV", "Testimonial", "data", "sectionHeading", "allTestimonial", "settings", "dots", "arrows", "infinite", "autoplay", "autoplaySpeed", "speed", "slidesToShow", "slidesToScroll", "initialSlide", "className", "style", "display", "visibility", "backgroundColor", "minHeight", "children", "src", "title", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "miniTitle", "variant", "map", "item", "index", "avatarImg", "reviewText", "avatar<PERSON><PERSON>", "avatarCompany", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Testimonial.jsx"], "sourcesContent": ["import React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\n\nexport default function Testimonial({ data }) {\n  const { sectionHeading, allTestimonial } = data;\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: false,\n    autoplaySpeed: 4000,\n    speed: 1000,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0,\n  };\n  return (\n    <section className=\"section effect-section pb-0\" style={{ display: 'block', visibility: 'visible', backgroundColor: 'rgba(0,0,255,0.1)', minHeight: '200px' }}>\n      <div className=\"effect-3\">\n        <img src=\"/images/effect-3.svg\" title alt=\"\" />\n      </div>\n      <div className=\"effect-4\">\n        <img src=\"/images/effect-4.svg\" title alt=\"\" />\n      </div>\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n          variant=\"text-center\"\n        />\n        <div data-aos=\"fade\" data-aos-duration=\"1200\" data-aos-delay=\"300\">\n          <Slider {...settings}>\n            {allTestimonial?.map((item, index) => (\n              <div key={index}>\n                <div className=\"testimonial-box\">\n                  <div className=\"t-user\">\n                    <img src={item.avatarImg} alt=\"Avatar\" />\n                  </div>\n                  <div className=\"t-text\">{item.reviewText}</div>\n                  <div className=\"t-person\">\n                    <h6>{item.avatarName}</h6>\n                    <span>{item.avatarCompany}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </Slider>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,MAAM,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,eAAe,SAASC,WAAWA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAC5C,MAAM;IAAEC,cAAc;IAAEC;EAAe,CAAC,GAAGF,IAAI;EAC/C,IAAIG,QAAQ,GAAG;IACbC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC;EACD,oBACEd,OAAA;IAASe,SAAS,EAAC,6BAA6B;IAACC,KAAK,EAAE;MAAEC,OAAO,EAAE,OAAO;MAAEC,UAAU,EAAE,SAAS;MAAEC,eAAe,EAAE,mBAAmB;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC5JrB,OAAA;MAAKe,SAAS,EAAC,UAAU;MAAAM,QAAA,eACvBrB,OAAA;QAAKsB,GAAG,EAAC,sBAAsB;QAACC,KAAK;QAACC,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eACN5B,OAAA;MAAKe,SAAS,EAAC,UAAU;MAAAM,QAAA,eACvBrB,OAAA;QAAKsB,GAAG,EAAC,sBAAsB;QAACC,KAAK;QAACC,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eACN5B,OAAA;MAAKe,SAAS,EAAC,WAAW;MAAAM,QAAA,gBACxBrB,OAAA,CAACH,cAAc;QACbgC,SAAS,EAAE1B,cAAc,CAAC0B,SAAU;QACpCN,KAAK,EAAEpB,cAAc,CAACoB,KAAM;QAC5BO,OAAO,EAAC;MAAa;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACF5B,OAAA;QAAK,YAAS,MAAM;QAAC,qBAAkB,MAAM;QAAC,kBAAe,KAAK;QAAAqB,QAAA,eAChErB,OAAA,CAACF,MAAM;UAAA,GAAKO,QAAQ;UAAAgB,QAAA,EACjBjB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/BjC,OAAA;YAAAqB,QAAA,eACErB,OAAA;cAAKe,SAAS,EAAC,iBAAiB;cAAAM,QAAA,gBAC9BrB,OAAA;gBAAKe,SAAS,EAAC,QAAQ;gBAAAM,QAAA,eACrBrB,OAAA;kBAAKsB,GAAG,EAAEU,IAAI,CAACE,SAAU;kBAACV,GAAG,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACN5B,OAAA;gBAAKe,SAAS,EAAC,QAAQ;gBAAAM,QAAA,EAAEW,IAAI,CAACG;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/C5B,OAAA;gBAAKe,SAAS,EAAC,UAAU;gBAAAM,QAAA,gBACvBrB,OAAA;kBAAAqB,QAAA,EAAKW,IAAI,CAACI;gBAAU;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1B5B,OAAA;kBAAAqB,QAAA,EAAOW,IAAI,CAACK;gBAAa;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAVEK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACU,EAAA,GAhDuBrC,WAAW;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}