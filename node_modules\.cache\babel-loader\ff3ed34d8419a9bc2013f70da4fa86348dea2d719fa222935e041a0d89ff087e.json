{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar style_to_object_1 = __importDefault(require(\"style-to-object\"));\nvar utilities_1 = require(\"./utilities\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style, options) {\n  var output = {};\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n  (0, style_to_object_1.default)(style, function (property, value) {\n    // skip CSS comment\n    if (property && value) {\n      output[(0, utilities_1.camelCase)(property, options)] = value;\n    }\n  });\n  return output;\n}\nexports.default = StyleToJS;", "map": {"version": 3, "names": ["style_to_object_1", "__importDefault", "require", "utilities_1", "StyleToJS", "style", "options", "output", "default", "property", "value", "camelCase", "exports"], "sources": ["C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\node_modules\\style-to-js\\src\\index.ts"], "sourcesContent": ["import StyleToObject from 'style-to-object';\nimport { camelCase, CamelCaseOptions } from './utilities';\n\ntype StyleObject = Record<string, string>;\n\ninterface StyleToJSOptions extends CamelCaseOptions {}\n\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nexport default function StyleToJS(\n  style: string,\n  options?: StyleToJSOptions,\n): StyleObject {\n  const output: StyleObject = {};\n\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n\n  StyleToObject(style, (property, value) => {\n    // skip CSS comment\n    if (property && value) {\n      output[camelCase(property, options)] = value;\n    }\n  });\n\n  return output;\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,iBAAA,GAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAMA;;;AAGA,SAAwBE,SAASA,CAC/BC,KAAa,EACbC,OAA0B;EAE1B,IAAMC,MAAM,GAAgB,EAAE;EAE9B,IAAI,CAACF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAOE,MAAM;;EAGf,IAAAP,iBAAA,CAAAQ,OAAa,EAACH,KAAK,EAAE,UAACI,QAAQ,EAAEC,KAAK;IACnC;IACA,IAAID,QAAQ,IAAIC,KAAK,EAAE;MACrBH,MAAM,CAAC,IAAAJ,WAAA,CAAAQ,SAAS,EAACF,QAAQ,EAAEH,OAAO,CAAC,CAAC,GAAGI,KAAK;;EAEhD,CAAC,CAAC;EAEF,OAAOH,MAAM;AACf;AAlBAK,OAAA,CAAAJ,OAAA,GAAAJ,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}