{"ast": null, "code": "export const isHeadless = navigator => {\n  return navigator.webdriver || !navigator.languages || navigator.languages.length === 0;\n};", "map": {"version": 3, "names": ["isHeadless", "navigator", "webdriver", "languages", "length"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/isHeadless/isHeadless.js"], "sourcesContent": ["export const isHeadless = (navigator) => {\n    return navigator.webdriver || !navigator.languages || navigator.languages.length === 0;\n};\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAIC,SAAS,IAAK;EACrC,OAAOA,SAAS,CAACC,SAAS,IAAI,CAACD,SAAS,CAACE,SAAS,IAAIF,SAAS,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC;AAC1F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}