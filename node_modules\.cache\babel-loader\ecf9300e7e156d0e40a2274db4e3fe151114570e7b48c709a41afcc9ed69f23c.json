{"ast": null, "code": "var React = require('react');\nvar attributesToProps = require('./attributes-to-props');\nvar utilities = require('./utilities');\nvar setStyleProp = utilities.setStyleProp;\nvar canTextBeChildOfNode = utilities.canTextBeChildOfNode;\n\n/**\n * Converts DOM nodes to JSX element(s).\n *\n * @param {DomElement[]} nodes - DOM nodes.\n * @param {object} [options={}] - Options.\n * @param {Function} [options.replace] - Replacer.\n * @param {Function} [options.transform] - Transform.\n * @param {object} [options.library] - Library (React, Preact, etc.).\n * @returns - String or JSX element(s).\n */\nfunction domToReact(nodes, options) {\n  options = options || {};\n  var library = options.library || React;\n  var cloneElement = library.cloneElement;\n  var createElement = library.createElement;\n  var isValidElement = library.isValidElement;\n  var result = [];\n  var node;\n  var isWhitespace;\n  var hasReplace = typeof options.replace === 'function';\n  var transform = options.transform || utilities.returnFirstArg;\n  var replaceElement;\n  var props;\n  var children;\n  var trim = options.trim;\n  for (var i = 0, len = nodes.length; i < len; i++) {\n    node = nodes[i];\n\n    // replace with custom React element (if present)\n    if (hasReplace) {\n      replaceElement = options.replace(node);\n      if (isValidElement(replaceElement)) {\n        // set \"key\" prop for sibling elements\n        // https://fb.me/react-warning-keys\n        if (len > 1) {\n          replaceElement = cloneElement(replaceElement, {\n            key: replaceElement.key || i\n          });\n        }\n        result.push(transform(replaceElement, node, i));\n        continue;\n      }\n    }\n    if (node.type === 'text') {\n      isWhitespace = !node.data.trim().length;\n      if (isWhitespace && node.parent && !canTextBeChildOfNode(node.parent)) {\n        // We have a whitespace node that can't be nested in its parent\n        // so skip it\n        continue;\n      }\n      if (trim && isWhitespace) {\n        // Trim is enabled and we have a whitespace node\n        // so skip it\n        continue;\n      }\n\n      // We have a text node that's not whitespace and it can be nested\n      // in its parent so add it to the results\n      result.push(transform(node.data, node, i));\n      continue;\n    }\n    props = node.attribs;\n    if (skipAttributesToProps(node)) {\n      setStyleProp(props.style, props);\n    } else if (props) {\n      props = attributesToProps(props, node.name);\n    }\n    children = null;\n    switch (node.type) {\n      case 'script':\n      case 'style':\n        // prevent text in <script> or <style> from being escaped\n        // https://reactjs.org/docs/dom-elements.html#dangerouslysetinnerhtml\n        if (node.children[0]) {\n          props.dangerouslySetInnerHTML = {\n            __html: node.children[0].data\n          };\n        }\n        break;\n      case 'tag':\n        // setting textarea value in children is an antipattern in React\n        // https://reactjs.org/docs/forms.html#the-textarea-tag\n        if (node.name === 'textarea' && node.children[0]) {\n          props.defaultValue = node.children[0].data;\n        } else if (node.children && node.children.length) {\n          // continue recursion of creating React elements (if applicable)\n          children = domToReact(node.children, options);\n        }\n        break;\n\n      // skip all other cases (e.g., comment)\n      default:\n        continue;\n    }\n\n    // set \"key\" prop for sibling elements\n    // https://fb.me/react-warning-keys\n    if (len > 1) {\n      props.key = i;\n    }\n    result.push(transform(createElement(node.name, props, children), node, i));\n  }\n  return result.length === 1 ? result[0] : result;\n}\n\n/**\n * Determines whether DOM element attributes should be transformed to props.\n * Web Components should not have their attributes transformed except for `style`.\n *\n * @param {DomElement} node\n * @returns - Whether node attributes should be converted to props.\n */\nfunction skipAttributesToProps(node) {\n  return utilities.PRESERVE_CUSTOM_ATTRIBUTES && node.type === 'tag' && utilities.isCustomComponent(node.name, node.attribs);\n}\nmodule.exports = domToReact;", "map": {"version": 3, "names": ["React", "require", "attributesToProps", "utilities", "setStyleProp", "canTextBeChildOfNode", "domToReact", "nodes", "options", "library", "cloneElement", "createElement", "isValidElement", "result", "node", "isWhitespace", "hasReplace", "replace", "transform", "returnFirstArg", "replaceElement", "props", "children", "trim", "i", "len", "length", "key", "push", "type", "data", "parent", "attribs", "skipAttributesToProps", "style", "name", "dangerouslySetInnerHTML", "__html", "defaultValue", "PRESERVE_CUSTOM_ATTRIBUTES", "isCustomComponent", "module", "exports"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/html-react-parser/lib/dom-to-react.js"], "sourcesContent": ["var React = require('react');\nvar attributesToProps = require('./attributes-to-props');\nvar utilities = require('./utilities');\n\nvar setStyleProp = utilities.setStyleProp;\nvar canTextBeChildOfNode = utilities.canTextBeChildOfNode;\n\n/**\n * Converts DOM nodes to JSX element(s).\n *\n * @param {DomElement[]} nodes - DOM nodes.\n * @param {object} [options={}] - Options.\n * @param {Function} [options.replace] - Replacer.\n * @param {Function} [options.transform] - Transform.\n * @param {object} [options.library] - Library (React, Preact, etc.).\n * @returns - String or JSX element(s).\n */\nfunction domToReact(nodes, options) {\n  options = options || {};\n\n  var library = options.library || React;\n  var cloneElement = library.cloneElement;\n  var createElement = library.createElement;\n  var isValidElement = library.isValidElement;\n\n  var result = [];\n  var node;\n  var isWhitespace;\n  var hasReplace = typeof options.replace === 'function';\n  var transform = options.transform || utilities.returnFirstArg;\n  var replaceElement;\n  var props;\n  var children;\n  var trim = options.trim;\n\n  for (var i = 0, len = nodes.length; i < len; i++) {\n    node = nodes[i];\n\n    // replace with custom React element (if present)\n    if (hasReplace) {\n      replaceElement = options.replace(node);\n\n      if (isValidElement(replaceElement)) {\n        // set \"key\" prop for sibling elements\n        // https://fb.me/react-warning-keys\n        if (len > 1) {\n          replaceElement = cloneElement(replaceElement, {\n            key: replaceElement.key || i\n          });\n        }\n        result.push(transform(replaceElement, node, i));\n        continue;\n      }\n    }\n\n    if (node.type === 'text') {\n      isWhitespace = !node.data.trim().length;\n\n      if (isWhitespace && node.parent && !canTextBeChildOfNode(node.parent)) {\n        // We have a whitespace node that can't be nested in its parent\n        // so skip it\n        continue;\n      }\n\n      if (trim && isWhitespace) {\n        // Trim is enabled and we have a whitespace node\n        // so skip it\n        continue;\n      }\n\n      // We have a text node that's not whitespace and it can be nested\n      // in its parent so add it to the results\n      result.push(transform(node.data, node, i));\n      continue;\n    }\n\n    props = node.attribs;\n    if (skipAttributesToProps(node)) {\n      setStyleProp(props.style, props);\n    } else if (props) {\n      props = attributesToProps(props, node.name);\n    }\n\n    children = null;\n\n    switch (node.type) {\n      case 'script':\n      case 'style':\n        // prevent text in <script> or <style> from being escaped\n        // https://reactjs.org/docs/dom-elements.html#dangerouslysetinnerhtml\n        if (node.children[0]) {\n          props.dangerouslySetInnerHTML = {\n            __html: node.children[0].data\n          };\n        }\n        break;\n\n      case 'tag':\n        // setting textarea value in children is an antipattern in React\n        // https://reactjs.org/docs/forms.html#the-textarea-tag\n        if (node.name === 'textarea' && node.children[0]) {\n          props.defaultValue = node.children[0].data;\n        } else if (node.children && node.children.length) {\n          // continue recursion of creating React elements (if applicable)\n          children = domToReact(node.children, options);\n        }\n        break;\n\n      // skip all other cases (e.g., comment)\n      default:\n        continue;\n    }\n\n    // set \"key\" prop for sibling elements\n    // https://fb.me/react-warning-keys\n    if (len > 1) {\n      props.key = i;\n    }\n\n    result.push(transform(createElement(node.name, props, children), node, i));\n  }\n\n  return result.length === 1 ? result[0] : result;\n}\n\n/**\n * Determines whether DOM element attributes should be transformed to props.\n * Web Components should not have their attributes transformed except for `style`.\n *\n * @param {DomElement} node\n * @returns - Whether node attributes should be converted to props.\n */\nfunction skipAttributesToProps(node) {\n  return (\n    utilities.PRESERVE_CUSTOM_ATTRIBUTES &&\n    node.type === 'tag' &&\n    utilities.isCustomComponent(node.name, node.attribs)\n  );\n}\n\nmodule.exports = domToReact;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,iBAAiB,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AACxD,IAAIE,SAAS,GAAGF,OAAO,CAAC,aAAa,CAAC;AAEtC,IAAIG,YAAY,GAAGD,SAAS,CAACC,YAAY;AACzC,IAAIC,oBAAoB,GAAGF,SAAS,CAACE,oBAAoB;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAIT,KAAK;EACtC,IAAIU,YAAY,GAAGD,OAAO,CAACC,YAAY;EACvC,IAAIC,aAAa,GAAGF,OAAO,CAACE,aAAa;EACzC,IAAIC,cAAc,GAAGH,OAAO,CAACG,cAAc;EAE3C,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,IAAI;EACR,IAAIC,YAAY;EAChB,IAAIC,UAAU,GAAG,OAAOR,OAAO,CAACS,OAAO,KAAK,UAAU;EACtD,IAAIC,SAAS,GAAGV,OAAO,CAACU,SAAS,IAAIf,SAAS,CAACgB,cAAc;EAC7D,IAAIC,cAAc;EAClB,IAAIC,KAAK;EACT,IAAIC,QAAQ;EACZ,IAAIC,IAAI,GAAGf,OAAO,CAACe,IAAI;EAEvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGlB,KAAK,CAACmB,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAChDV,IAAI,GAAGP,KAAK,CAACiB,CAAC,CAAC;;IAEf;IACA,IAAIR,UAAU,EAAE;MACdI,cAAc,GAAGZ,OAAO,CAACS,OAAO,CAACH,IAAI,CAAC;MAEtC,IAAIF,cAAc,CAACQ,cAAc,CAAC,EAAE;QAClC;QACA;QACA,IAAIK,GAAG,GAAG,CAAC,EAAE;UACXL,cAAc,GAAGV,YAAY,CAACU,cAAc,EAAE;YAC5CO,GAAG,EAAEP,cAAc,CAACO,GAAG,IAAIH;UAC7B,CAAC,CAAC;QACJ;QACAX,MAAM,CAACe,IAAI,CAACV,SAAS,CAACE,cAAc,EAAEN,IAAI,EAAEU,CAAC,CAAC,CAAC;QAC/C;MACF;IACF;IAEA,IAAIV,IAAI,CAACe,IAAI,KAAK,MAAM,EAAE;MACxBd,YAAY,GAAG,CAACD,IAAI,CAACgB,IAAI,CAACP,IAAI,CAAC,CAAC,CAACG,MAAM;MAEvC,IAAIX,YAAY,IAAID,IAAI,CAACiB,MAAM,IAAI,CAAC1B,oBAAoB,CAACS,IAAI,CAACiB,MAAM,CAAC,EAAE;QACrE;QACA;QACA;MACF;MAEA,IAAIR,IAAI,IAAIR,YAAY,EAAE;QACxB;QACA;QACA;MACF;;MAEA;MACA;MACAF,MAAM,CAACe,IAAI,CAACV,SAAS,CAACJ,IAAI,CAACgB,IAAI,EAAEhB,IAAI,EAAEU,CAAC,CAAC,CAAC;MAC1C;IACF;IAEAH,KAAK,GAAGP,IAAI,CAACkB,OAAO;IACpB,IAAIC,qBAAqB,CAACnB,IAAI,CAAC,EAAE;MAC/BV,YAAY,CAACiB,KAAK,CAACa,KAAK,EAAEb,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIA,KAAK,EAAE;MAChBA,KAAK,GAAGnB,iBAAiB,CAACmB,KAAK,EAAEP,IAAI,CAACqB,IAAI,CAAC;IAC7C;IAEAb,QAAQ,GAAG,IAAI;IAEf,QAAQR,IAAI,CAACe,IAAI;MACf,KAAK,QAAQ;MACb,KAAK,OAAO;QACV;QACA;QACA,IAAIf,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,EAAE;UACpBD,KAAK,CAACe,uBAAuB,GAAG;YAC9BC,MAAM,EAAEvB,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACQ;UAC3B,CAAC;QACH;QACA;MAEF,KAAK,KAAK;QACR;QACA;QACA,IAAIhB,IAAI,CAACqB,IAAI,KAAK,UAAU,IAAIrB,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,EAAE;UAChDD,KAAK,CAACiB,YAAY,GAAGxB,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACQ,IAAI;QAC5C,CAAC,MAAM,IAAIhB,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACQ,QAAQ,CAACI,MAAM,EAAE;UAChD;UACAJ,QAAQ,GAAGhB,UAAU,CAACQ,IAAI,CAACQ,QAAQ,EAAEd,OAAO,CAAC;QAC/C;QACA;;MAEF;MACA;QACE;IACJ;;IAEA;IACA;IACA,IAAIiB,GAAG,GAAG,CAAC,EAAE;MACXJ,KAAK,CAACM,GAAG,GAAGH,CAAC;IACf;IAEAX,MAAM,CAACe,IAAI,CAACV,SAAS,CAACP,aAAa,CAACG,IAAI,CAACqB,IAAI,EAAEd,KAAK,EAAEC,QAAQ,CAAC,EAAER,IAAI,EAAEU,CAAC,CAAC,CAAC;EAC5E;EAEA,OAAOX,MAAM,CAACa,MAAM,KAAK,CAAC,GAAGb,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,qBAAqBA,CAACnB,IAAI,EAAE;EACnC,OACEX,SAAS,CAACoC,0BAA0B,IACpCzB,IAAI,CAACe,IAAI,KAAK,KAAK,IACnB1B,SAAS,CAACqC,iBAAiB,CAAC1B,IAAI,CAACqB,IAAI,EAAErB,IAAI,CAACkB,OAAO,CAAC;AAExD;AAEAS,MAAM,CAACC,OAAO,GAAGpC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}