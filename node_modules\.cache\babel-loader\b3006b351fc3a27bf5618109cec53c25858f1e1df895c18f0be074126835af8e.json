{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Testimonial.jsx\";\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Testimonial({\n  data\n}) {\n  console.log('Testimonial data:', data);\n  if (!data) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Testimonial: No data received\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 12\n    }, this);\n  }\n  const {\n    sectionHeading,\n    allTestimonial\n  } = data;\n  console.log('Testimonial allTestimonial:', allTestimonial);\n  if (!allTestimonial) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Testimonial: No allTestimonial data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 12\n    }, this);\n  }\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: false,\n    autoplaySpeed: 4000,\n    speed: 1000,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section effect-section pb-0\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"effect-3\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/effect-3.svg\",\n        title: true,\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"effect-4\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/images/effect-4.svg\",\n        title: true,\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeading, {\n        miniTitle: sectionHeading.miniTitle,\n        title: sectionHeading.title,\n        variant: \"text-center\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        \"data-aos\": \"fade\",\n        \"data-aos-duration\": \"1200\",\n        \"data-aos-delay\": \"300\",\n        children: /*#__PURE__*/_jsxDEV(Slider, {\n          ...settings,\n          children: allTestimonial === null || allTestimonial === void 0 ? void 0 : allTestimonial.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"t-user\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: item.avatarImg,\n                  alt: \"Avatar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"t-text\",\n                children: item.reviewText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"t-person\",\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: item.avatarName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: item.avatarCompany\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_c = Testimonial;\nvar _c;\n$RefreshReg$(_c, \"Testimonial\");", "map": {"version": 3, "names": ["React", "SectionHeading", "Slide<PERSON>", "jsxDEV", "_jsxDEV", "Testimonial", "data", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sectionHeading", "allTestimonial", "settings", "dots", "arrows", "infinite", "autoplay", "autoplaySpeed", "speed", "slidesToShow", "slidesToScroll", "initialSlide", "className", "src", "title", "alt", "miniTitle", "variant", "map", "item", "index", "avatarImg", "reviewText", "avatar<PERSON><PERSON>", "avatarCompany", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Testimonial.jsx"], "sourcesContent": ["import React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\n\nexport default function Testimonial({ data }) {\n  console.log('Testimonial data:', data);\n\n  if (!data) {\n    return <div>Testimonial: No data received</div>;\n  }\n\n  const { sectionHeading, allTestimonial } = data;\n  console.log('Testimonial allTestimonial:', allTestimonial);\n\n  if (!allTestimonial) {\n    return <div>Testimonial: No allTestimonial data</div>;\n  }\n\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: false,\n    autoplaySpeed: 4000,\n    speed: 1000,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0,\n  };\n  return (\n    <section className=\"section effect-section pb-0\">\n      <div className=\"effect-3\">\n        <img src=\"/images/effect-3.svg\" title alt=\"\" />\n      </div>\n      <div className=\"effect-4\">\n        <img src=\"/images/effect-4.svg\" title alt=\"\" />\n      </div>\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n          variant=\"text-center\"\n        />\n        <div data-aos=\"fade\" data-aos-duration=\"1200\" data-aos-delay=\"300\">\n          <Slider {...settings}>\n            {allTestimonial?.map((item, index) => (\n              <div key={index}>\n                <div className=\"testimonial-box\">\n                  <div className=\"t-user\">\n                    <img src={item.avatarImg} alt=\"Avatar\" />\n                  </div>\n                  <div className=\"t-text\">{item.reviewText}</div>\n                  <div className=\"t-person\">\n                    <h6>{item.avatarName}</h6>\n                    <span>{item.avatarCompany}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </Slider>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,MAAM,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,eAAe,SAASC,WAAWA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAC5CC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,IAAI,CAAC;EAEtC,IAAI,CAACA,IAAI,EAAE;IACT,oBAAOF,OAAA;MAAAK,QAAA,EAAK;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjD;EAEA,MAAM;IAAEC,cAAc;IAAEC;EAAe,CAAC,GAAGT,IAAI;EAC/CC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEO,cAAc,CAAC;EAE1D,IAAI,CAACA,cAAc,EAAE;IACnB,oBAAOX,OAAA;MAAAK,QAAA,EAAK;IAAmC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACvD;EAEA,IAAIG,QAAQ,GAAG;IACbC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC;EACD,oBACErB,OAAA;IAASsB,SAAS,EAAC,6BAA6B;IAAAjB,QAAA,gBAC9CL,OAAA;MAAKsB,SAAS,EAAC,UAAU;MAAAjB,QAAA,eACvBL,OAAA;QAAKuB,GAAG,EAAC,sBAAsB;QAACC,KAAK;QAACC,GAAG,EAAC;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eACNT,OAAA;MAAKsB,SAAS,EAAC,UAAU;MAAAjB,QAAA,eACvBL,OAAA;QAAKuB,GAAG,EAAC,sBAAsB;QAACC,KAAK;QAACC,GAAG,EAAC;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eACNT,OAAA;MAAKsB,SAAS,EAAC,WAAW;MAAAjB,QAAA,gBACxBL,OAAA,CAACH,cAAc;QACb6B,SAAS,EAAEhB,cAAc,CAACgB,SAAU;QACpCF,KAAK,EAAEd,cAAc,CAACc,KAAM;QAC5BG,OAAO,EAAC;MAAa;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACFT,OAAA;QAAK,YAAS,MAAM;QAAC,qBAAkB,MAAM;QAAC,kBAAe,KAAK;QAAAK,QAAA,eAChEL,OAAA,CAACF,MAAM;UAAA,GAAKc,QAAQ;UAAAP,QAAA,EACjBM,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/B9B,OAAA;YAAAK,QAAA,eACEL,OAAA;cAAKsB,SAAS,EAAC,iBAAiB;cAAAjB,QAAA,gBAC9BL,OAAA;gBAAKsB,SAAS,EAAC,QAAQ;gBAAAjB,QAAA,eACrBL,OAAA;kBAAKuB,GAAG,EAAEM,IAAI,CAACE,SAAU;kBAACN,GAAG,EAAC;gBAAQ;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACNT,OAAA;gBAAKsB,SAAS,EAAC,QAAQ;gBAAAjB,QAAA,EAAEwB,IAAI,CAACG;cAAU;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CT,OAAA;gBAAKsB,SAAS,EAAC,UAAU;gBAAAjB,QAAA,gBACvBL,OAAA;kBAAAK,QAAA,EAAKwB,IAAI,CAACI;gBAAU;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1BT,OAAA;kBAAAK,QAAA,EAAOwB,IAAI,CAACK;gBAAa;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAVEqB,KAAK;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAAC0B,EAAA,GA5DuBlC,WAAW;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}