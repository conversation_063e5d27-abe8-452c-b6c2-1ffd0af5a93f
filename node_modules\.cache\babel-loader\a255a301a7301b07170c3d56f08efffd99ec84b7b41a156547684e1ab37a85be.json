{"ast": null, "code": "export const validateTemplateParams = templateParams => {\n  // eslint-disable-next-line @typescript-eslint/no-base-to-string\n  if (templateParams && templateParams.toString() !== '[object Object]') {\n    throw 'The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/';\n  }\n};", "map": {"version": 3, "names": ["validateTemplateParams", "templateParams", "toString"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/validateTemplateParams/validateTemplateParams.js"], "sourcesContent": ["export const validateTemplateParams = (templateParams) => {\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    if (templateParams && templateParams.toString() !== '[object Object]') {\n        throw 'The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/';\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB,GAAIC,cAAc,IAAK;EACtD;EACA,IAAIA,cAAc,IAAIA,cAAc,CAACC,QAAQ,CAAC,CAAC,KAAK,iBAAiB,EAAE;IACnE,MAAM,yFAAyF;EACnG;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}