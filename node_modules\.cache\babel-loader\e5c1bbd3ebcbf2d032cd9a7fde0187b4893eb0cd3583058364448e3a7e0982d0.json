{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Service.jsx\";\nimport { Icon } from '@iconify/react';\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Ratings from './Ratings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Service({\n  data\n}) {\n  const {\n    sectionHeading,\n    allService\n  } = data;\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section\",\n    id: \"services\",\n    style: {\n      display: 'block',\n      visibility: 'visible'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeading, {\n        miniTitle: sectionHeading.miniTitle,\n        title: sectionHeading.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row gy-5\",\n        children: allService === null || allService === void 0 ? void 0 : allService.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-sm-6 col-lg-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"services-box\",\n            style: {\n              backgroundImage: `url(${item.imgUrl})`\n            },\n            \"data-aos\": \"fade-left\",\n            \"data-aos-duration\": \"1200\",\n            \"data-aos-delay\": index * 100,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"services-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"icon\",\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  icon: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: item.subTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rating-wrap\",\n                children: /*#__PURE__*/_jsxDEV(Ratings, {\n                  ratings: item.ratings\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = Service;\nvar _c;\n$RefreshReg$(_c, \"Service\");", "map": {"version": 3, "names": ["Icon", "React", "SectionHeading", "Ratings", "jsxDEV", "_jsxDEV", "Service", "data", "sectionHeading", "allService", "className", "id", "style", "display", "visibility", "children", "miniTitle", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "backgroundImage", "imgUrl", "icon", "subTitle", "ratings", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Service.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Ratings from './Ratings';\n\nexport default function Service({ data }) {\n  const { sectionHeading, allService } = data;\n  return (\n    <section className=\"section\" id=\"services\" style={{ display: 'block', visibility: 'visible' }}>\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n        />\n        <div className=\"row gy-5\">\n          {allService?.map((item, index) => (\n            <div className=\"col-sm-6 col-lg-3\" key={index}>\n              <div\n                className=\"services-box\"\n                style={{ backgroundImage: `url(${item.imgUrl})` }}\n                data-aos=\"fade-left\"\n                data-aos-duration=\"1200\"\n                data-aos-delay={index * 100}\n              >\n                <div className=\"services-body\">\n                  <div className=\"icon\">\n                    <Icon icon={item.icon} />\n                  </div>\n                  <h5>{item.title}</h5>\n                  <p>{item.subTitle}</p>\n                  <div className=\"rating-wrap\">\n                    <Ratings ratings={item.ratings} />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,OAAO,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,eAAe,SAASC,OAAOA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACxC,MAAM;IAAEC,cAAc;IAAEC;EAAW,CAAC,GAAGF,IAAI;EAC3C,oBACEF,OAAA;IAASK,SAAS,EAAC,SAAS;IAACC,EAAE,EAAC,UAAU;IAACC,KAAK,EAAE;MAAEC,OAAO,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC5FV,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAK,QAAA,gBACxBV,OAAA,CAACH,cAAc;QACbc,SAAS,EAAER,cAAc,CAACQ,SAAU;QACpCC,KAAK,EAAET,cAAc,CAACS;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFhB,OAAA;QAAKK,SAAS,EAAC,UAAU;QAAAK,QAAA,EACtBN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BnB,OAAA;UAAKK,SAAS,EAAC,mBAAmB;UAAAK,QAAA,eAChCV,OAAA;YACEK,SAAS,EAAC,cAAc;YACxBE,KAAK,EAAE;cAAEa,eAAe,EAAE,OAAOF,IAAI,CAACG,MAAM;YAAI,CAAE;YAClD,YAAS,WAAW;YACpB,qBAAkB,MAAM;YACxB,kBAAgBF,KAAK,GAAG,GAAI;YAAAT,QAAA,eAE5BV,OAAA;cAAKK,SAAS,EAAC,eAAe;cAAAK,QAAA,gBAC5BV,OAAA;gBAAKK,SAAS,EAAC,MAAM;gBAAAK,QAAA,eACnBV,OAAA,CAACL,IAAI;kBAAC2B,IAAI,EAAEJ,IAAI,CAACI;gBAAK;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNhB,OAAA;gBAAAU,QAAA,EAAKQ,IAAI,CAACN;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBhB,OAAA;gBAAAU,QAAA,EAAIQ,IAAI,CAACK;cAAQ;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBhB,OAAA;gBAAKK,SAAS,EAAC,aAAa;gBAAAK,QAAA,eAC1BV,OAAA,CAACF,OAAO;kBAAC0B,OAAO,EAAEN,IAAI,CAACM;gBAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAlBgCG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBxC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACS,EAAA,GApCuBxB,OAAO;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}