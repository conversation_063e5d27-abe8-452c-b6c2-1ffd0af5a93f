{"ast": null, "code": "import{Icon}from'@iconify/react';import React,{useState}from'react';import SectionHeading from'./SectionHeading';import Slider from'react-slick';import Modal from'./Modal';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";export default function Projects(_ref){let{data}=_ref;const[modal,setModal]=useState(false);const[modalType,setModalType]=useState('image');const[modalData,setModalData]=useState({});const{sectionHeading,allProjects}=data;const handelProjectDetails=(item,itemType)=>{if(itemType==='image'){setModalData(item);}else{setModalData(item);}setModalType(itemType);setModal(!modal);console.log(modalType);};var settings={dots:true,arrows:false,infinite:true,autoplay:true,autoplaySpeed:3000,speed:700,slidesToShow:1,slidesToScroll:1,initialSlide:0,variableWidth:true,pauseOnHover:true,pauseOnFocus:true,cssEase:'ease-in-out',responsive:[{breakpoint:1024,settings:{slidesToShow:1,slidesToScroll:1,variableWidth:true}},{breakpoint:768,settings:{slidesToShow:1,slidesToScroll:1,variableWidth:false,centerMode:true,centerPadding:'20px'}},{breakpoint:480,settings:{slidesToShow:1,slidesToScroll:1,variableWidth:false,centerMode:true,centerPadding:'10px',dots:true}},{breakpoint:375,settings:{slidesToShow:1,slidesToScroll:1,variableWidth:false,centerMode:false,centerPadding:'0px',dots:true}}]};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"section\",{className:\"project-section section gray-bg\",id:\"project\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(SectionHeading,{miniTitle:sectionHeading.miniTitle,title:sectionHeading.title}),/*#__PURE__*/_jsx(\"div\",{className:\"full-width\",\"data-aos\":\"fade\",\"data-aos-duration\":\"1200\",\"data-aos-delay\":\"400\",children:/*#__PURE__*/_jsx(Slider,{...settings,className:\"slider-gap-24\",children:allProjects===null||allProjects===void 0?void 0:allProjects.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"project-slide-item\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"project-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"project-media\",children:[/*#__PURE__*/_jsx(\"img\",{src:item.thumbUrl,alt:\"Thumb\"}),/*#__PURE__*/_jsx(\"span\",{className:\"gallery-link\",onClick:()=>handelProjectDetails(item,'image'),children:/*#__PURE__*/_jsx(\"i\",{children:/*#__PURE__*/_jsx(Icon,{icon:\"bi:plus\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"project-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text\",children:[/*#__PURE__*/_jsx(\"h5\",{children:item.title}),/*#__PURE__*/_jsx(\"span\",{children:item.subTitle})]}),/*#__PURE__*/_jsx(\"div\",{className:\"link\",children:/*#__PURE__*/_jsx(\"span\",{className:\"p-link\",onClick:()=>handelProjectDetails(item,'details'),children:/*#__PURE__*/_jsx(Icon,{icon:\"bi:arrow-right\"})})})]})]})},index))})})]})}),modal&&/*#__PURE__*/_jsx(\"div\",{className:\"mfp-wrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mfp-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mfp-bg\",onClick:()=>setModal(!modal)}),/*#__PURE__*/_jsxs(\"div\",{className:\"mfp-content\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"mfp-close\",onClick:()=>setModal(!modal),children:\"\\xD7\"}),modalType==='image'?/*#__PURE__*/_jsx(\"img\",{src:modalData.thumbUrl,alt:\"Thumbnail\"}):/*#__PURE__*/_jsx(Modal,{modalData:modalData})]})]})})]});}", "map": {"version": 3, "names": ["Icon", "React", "useState", "SectionHeading", "Slide<PERSON>", "Modal", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Projects", "_ref", "data", "modal", "setModal", "modalType", "setModalType", "modalData", "setModalData", "sectionHeading", "allProjects", "handelProjectDetails", "item", "itemType", "console", "log", "settings", "dots", "arrows", "infinite", "autoplay", "autoplaySpeed", "speed", "slidesToShow", "slidesToScroll", "initialSlide", "variableWidth", "pauseOnHover", "pauseOnFocus", "cssEase", "responsive", "breakpoint", "centerMode", "centerPadding", "children", "className", "id", "miniTitle", "title", "map", "index", "src", "thumbUrl", "alt", "onClick", "icon", "subTitle", "type"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Projects.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React, { useState } from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\nimport Modal from './Modal';\n\nexport default function Projects({ data }) {\n  const [modal, setModal] = useState(false);\n  const [modalType, setModalType] = useState('image');\n  const [modalData, setModalData] = useState({});\n  const { sectionHeading, allProjects } = data;\n  const handelProjectDetails = (item, itemType) => {\n    if (itemType === 'image') {\n      setModalData(item);\n    } else {\n      setModalData(item);\n    }\n    setModalType(itemType);\n\n    setModal(!modal);\n    console.log(modalType);\n  };\n\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: true,\n    autoplaySpeed: 3000,\n    speed: 700,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0,\n    variableWidth: true,\n    pauseOnHover: true,\n    pauseOnFocus: true,\n    cssEase: 'ease-in-out',\n    responsive: [\n      {\n        breakpoint: 1024,\n        settings: {\n          slidesToShow: 1,\n          slidesToScroll: 1,\n          variableWidth: true,\n        }\n      },\n      {\n        breakpoint: 768,\n        settings: {\n          slidesToShow: 1,\n          slidesToScroll: 1,\n          variableWidth: false,\n          centerMode: true,\n          centerPadding: '20px',\n        }\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 1,\n          slidesToScroll: 1,\n          variableWidth: false,\n          centerMode: true,\n          centerPadding: '10px',\n          dots: true,\n        }\n      },\n      {\n        breakpoint: 375,\n        settings: {\n          slidesToShow: 1,\n          slidesToScroll: 1,\n          variableWidth: false,\n          centerMode: false,\n          centerPadding: '0px',\n          dots: true,\n        }\n      }\n    ]\n  };\n\n  return (\n    <>\n      <section className=\"project-section section gray-bg\" id=\"project\">\n        <div className=\"container\">\n          <SectionHeading\n            miniTitle={sectionHeading.miniTitle}\n            title={sectionHeading.title}\n          />\n          <div\n            className=\"full-width\"\n            data-aos=\"fade\"\n            data-aos-duration=\"1200\"\n            data-aos-delay=\"400\"\n          >\n            <Slider {...settings} className=\"slider-gap-24\">\n              {allProjects?.map((item, index) => (\n                <div key={index} className=\"project-slide-item\">\n                  <div className=\"project-box\">\n                    <div className=\"project-media\">\n                      <img src={item.thumbUrl} alt=\"Thumb\" />\n                      <span\n                        className=\"gallery-link\"\n                        onClick={() => handelProjectDetails(item, 'image')}\n                      >\n                        <i>\n                          <Icon icon=\"bi:plus\" />\n                        </i>\n                      </span>\n                    </div>\n                    <div className=\"project-body\">\n                      <div className=\"text\">\n                        <h5>{item.title}</h5>\n                        <span>{item.subTitle}</span>\n                      </div>\n                      <div className=\"link\">\n                        <span\n                          className=\"p-link\"\n                          onClick={() => handelProjectDetails(item, 'details')}\n                        >\n                          <Icon icon=\"bi:arrow-right\" />\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </Slider>\n          </div>\n        </div>\n      </section>\n      {modal && (\n        <div className=\"mfp-wrap\">\n          <div className=\"mfp-container\">\n            <div className=\"mfp-bg\" onClick={() => setModal(!modal)}></div>\n            <div className=\"mfp-content\">\n              <button\n                type=\"button\"\n                className=\"mfp-close\"\n                onClick={() => setModal(!modal)}\n              >\n                ×\n              </button>\n              {modalType === 'image' ? (\n                <img src={modalData.thumbUrl} alt=\"Thumbnail\" />\n              ) : (\n                <Modal modalData={modalData} />\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "mappings": "AAAA,OAASA,IAAI,KAAQ,gBAAgB,CACrC,MAAO,CAAAC,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,MAAM,KAAM,aAAa,CAChC,MAAO,CAAAC,KAAK,KAAM,SAAS,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE5B,cAAe,SAAS,CAAAC,QAAQA,CAAAC,IAAA,CAAW,IAAV,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACzC,KAAM,CAACe,SAAS,CAAEC,YAAY,CAAC,CAAGhB,QAAQ,CAAC,OAAO,CAAC,CACnD,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9C,KAAM,CAAEmB,cAAc,CAAEC,WAAY,CAAC,CAAGR,IAAI,CAC5C,KAAM,CAAAS,oBAAoB,CAAGA,CAACC,IAAI,CAAEC,QAAQ,GAAK,CAC/C,GAAIA,QAAQ,GAAK,OAAO,CAAE,CACxBL,YAAY,CAACI,IAAI,CAAC,CACpB,CAAC,IAAM,CACLJ,YAAY,CAACI,IAAI,CAAC,CACpB,CACAN,YAAY,CAACO,QAAQ,CAAC,CAEtBT,QAAQ,CAAC,CAACD,KAAK,CAAC,CAChBW,OAAO,CAACC,GAAG,CAACV,SAAS,CAAC,CACxB,CAAC,CAED,GAAI,CAAAW,QAAQ,CAAG,CACbC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,KAAK,CACbC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,IAAI,CACnBC,KAAK,CAAE,GAAG,CACVC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,IAAI,CACnBC,YAAY,CAAE,IAAI,CAClBC,YAAY,CAAE,IAAI,CAClBC,OAAO,CAAE,aAAa,CACtBC,UAAU,CAAE,CACV,CACEC,UAAU,CAAE,IAAI,CAChBf,QAAQ,CAAE,CACRO,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBE,aAAa,CAAE,IACjB,CACF,CAAC,CACD,CACEK,UAAU,CAAE,GAAG,CACff,QAAQ,CAAE,CACRO,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBE,aAAa,CAAE,KAAK,CACpBM,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,MACjB,CACF,CAAC,CACD,CACEF,UAAU,CAAE,GAAG,CACff,QAAQ,CAAE,CACRO,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBE,aAAa,CAAE,KAAK,CACpBM,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,MAAM,CACrBhB,IAAI,CAAE,IACR,CACF,CAAC,CACD,CACEc,UAAU,CAAE,GAAG,CACff,QAAQ,CAAE,CACRO,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBE,aAAa,CAAE,KAAK,CACpBM,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,KAAK,CACpBhB,IAAI,CAAE,IACR,CACF,CAAC,CAEL,CAAC,CAED,mBACEpB,KAAA,CAAAE,SAAA,EAAAmC,QAAA,eACEvC,IAAA,YAASwC,SAAS,CAAC,iCAAiC,CAACC,EAAE,CAAC,SAAS,CAAAF,QAAA,cAC/DrC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBvC,IAAA,CAACJ,cAAc,EACb8C,SAAS,CAAE5B,cAAc,CAAC4B,SAAU,CACpCC,KAAK,CAAE7B,cAAc,CAAC6B,KAAM,CAC7B,CAAC,cACF3C,IAAA,QACEwC,SAAS,CAAC,YAAY,CACtB,WAAS,MAAM,CACf,oBAAkB,MAAM,CACxB,iBAAe,KAAK,CAAAD,QAAA,cAEpBvC,IAAA,CAACH,MAAM,KAAKwB,QAAQ,CAAEmB,SAAS,CAAC,eAAe,CAAAD,QAAA,CAC5CxB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE6B,GAAG,CAAC,CAAC3B,IAAI,CAAE4B,KAAK,gBAC5B7C,IAAA,QAAiBwC,SAAS,CAAC,oBAAoB,CAAAD,QAAA,cAC7CrC,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BrC,KAAA,QAAKsC,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BvC,IAAA,QAAK8C,GAAG,CAAE7B,IAAI,CAAC8B,QAAS,CAACC,GAAG,CAAC,OAAO,CAAE,CAAC,cACvChD,IAAA,SACEwC,SAAS,CAAC,cAAc,CACxBS,OAAO,CAAEA,CAAA,GAAMjC,oBAAoB,CAACC,IAAI,CAAE,OAAO,CAAE,CAAAsB,QAAA,cAEnDvC,IAAA,MAAAuC,QAAA,cACEvC,IAAA,CAACP,IAAI,EAACyD,IAAI,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,CACA,CAAC,EACJ,CAAC,cACNhD,KAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BrC,KAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBvC,IAAA,OAAAuC,QAAA,CAAKtB,IAAI,CAAC0B,KAAK,CAAK,CAAC,cACrB3C,IAAA,SAAAuC,QAAA,CAAOtB,IAAI,CAACkC,QAAQ,CAAO,CAAC,EACzB,CAAC,cACNnD,IAAA,QAAKwC,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBvC,IAAA,SACEwC,SAAS,CAAC,QAAQ,CAClBS,OAAO,CAAEA,CAAA,GAAMjC,oBAAoB,CAACC,IAAI,CAAE,SAAS,CAAE,CAAAsB,QAAA,cAErDvC,IAAA,CAACP,IAAI,EAACyD,IAAI,CAAC,gBAAgB,CAAE,CAAC,CAC1B,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,EA3BEL,KA4BL,CACN,CAAC,CACI,CAAC,CACN,CAAC,EACH,CAAC,CACC,CAAC,CACTrC,KAAK,eACJR,IAAA,QAAKwC,SAAS,CAAC,UAAU,CAAAD,QAAA,cACvBrC,KAAA,QAAKsC,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BvC,IAAA,QAAKwC,SAAS,CAAC,QAAQ,CAACS,OAAO,CAAEA,CAAA,GAAMxC,QAAQ,CAAC,CAACD,KAAK,CAAE,CAAM,CAAC,cAC/DN,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BvC,IAAA,WACEoD,IAAI,CAAC,QAAQ,CACbZ,SAAS,CAAC,WAAW,CACrBS,OAAO,CAAEA,CAAA,GAAMxC,QAAQ,CAAC,CAACD,KAAK,CAAE,CAAA+B,QAAA,CACjC,MAED,CAAQ,CAAC,CACR7B,SAAS,GAAK,OAAO,cACpBV,IAAA,QAAK8C,GAAG,CAAElC,SAAS,CAACmC,QAAS,CAACC,GAAG,CAAC,WAAW,CAAE,CAAC,cAEhDhD,IAAA,CAACF,KAAK,EAACc,SAAS,CAAEA,SAAU,CAAE,CAC/B,EACE,CAAC,EACH,CAAC,CACH,CACN,EACD,CAAC,CAEP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}