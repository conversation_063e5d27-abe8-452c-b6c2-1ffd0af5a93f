{"version": 3, "sources": ["../src/utils/propValidator.ts", "../src/utils/cssTransition.tsx", "../src/utils/collapseToast.ts", "../src/utils/mapper.ts", "../src/components/CloseButton.tsx", "../src/components/ProgressBar.tsx", "../src/components/ToastContainer.tsx", "../src/core/genToastId.ts", "../src/core/containerObserver.ts", "../src/core/store.ts", "../src/core/toast.ts", "../src/hooks/useToastContainer.ts", "../src/hooks/useToast.ts", "../src/hooks/useIsomorphicLayoutEffect.ts", "../src/components/Toast.tsx", "../src/components/Icons.tsx", "../src/components/Transitions.tsx"], "sourcesContent": ["import { isValidElement } from 'react';\nimport { Id } from '../types';\n\nexport const isNum = (v: any): v is Number => typeof v === 'number' && !isNaN(v);\n\nexport const isStr = (v: any): v is String => typeof v === 'string';\n\nexport const isFn = (v: any): v is Function => typeof v === 'function';\n\nexport const isId = (v: unknown): v is Id => isStr(v) || isNum(v);\n\nexport const parseClassName = (v: any) => (isStr(v) || isFn(v) ? v : null);\n\nexport const getAutoCloseDelay = (toastAutoClose?: false | number, containerAutoClose?: false | number) =>\n  toastAutoClose === false || (isNum(toastAutoClose) && toastAutoClose > 0) ? toastAutoClose : containerAutoClose;\n\nexport const canBeRendered = <T>(content: T): boolean =>\n  isValidElement(content) || isStr(content) || isFn(content) || isNum(content);\n", "import React, { useEffect, useLayoutEffect, useRef } from 'react';\nimport { collapseToast } from './collapseToast';\nimport { Default } from './constant';\n\nimport { ToastTransitionProps } from '../types';\n\nexport interface CSSTransitionProps {\n  /**\n   * Css class to apply when toast enter\n   */\n  enter: string;\n\n  /**\n   * Css class to apply when toast leave\n   */\n  exit: string;\n\n  /**\n   * Append current toast position to the classname.\n   * If multiple classes are provided, only the last one will get the position\n   * For instance `myclass--top-center`...\n   * `Default: false`\n   */\n  appendPosition?: boolean;\n\n  /**\n   * Collapse toast smoothly when exit animation end\n   * `Default: true`\n   */\n  collapse?: boolean;\n\n  /**\n   * Collapse transition duration\n   * `Default: 300`\n   */\n  collapseDuration?: number;\n}\n\nconst enum AnimationStep {\n  Enter,\n  Exit\n}\n\n/**\n * Css animation that just work.\n * You could use animate.css for instance\n *\n *\n * ```\n * cssTransition({\n *   enter: \"animate__animated animate__bounceIn\",\n *   exit: \"animate__animated animate__bounceOut\"\n * })\n * ```\n *\n */\nexport function cssTransition({\n  enter,\n  exit,\n  appendPosition = false,\n  collapse = true,\n  collapseDuration = Default.COLLAPSE_DURATION\n}: CSSTransitionProps) {\n  return function ToastTransition({\n    children,\n    position,\n    preventExitTransition,\n    done,\n    nodeRef,\n    isIn,\n    playToast\n  }: ToastTransitionProps) {\n    const enterClassName = appendPosition ? `${enter}--${position}` : enter;\n    const exitClassName = appendPosition ? `${exit}--${position}` : exit;\n    const animationStep = useRef(AnimationStep.Enter);\n\n    useLayoutEffect(() => {\n      const node = nodeRef.current!;\n      const classToToken = enterClassName.split(' ');\n\n      const onEntered = (e: AnimationEvent) => {\n        if (e.target !== nodeRef.current) return;\n\n        playToast();\n        node.removeEventListener('animationend', onEntered);\n        node.removeEventListener('animationcancel', onEntered);\n        if (animationStep.current === AnimationStep.Enter && e.type !== 'animationcancel') {\n          node.classList.remove(...classToToken);\n        }\n      };\n\n      const onEnter = () => {\n        node.classList.add(...classToToken);\n        node.addEventListener('animationend', onEntered);\n        node.addEventListener('animationcancel', onEntered);\n      };\n\n      onEnter();\n    }, []);\n\n    useEffect(() => {\n      const node = nodeRef.current!;\n\n      const onExited = () => {\n        node.removeEventListener('animationend', onExited);\n        collapse ? collapseToast(node, done, collapseDuration) : done();\n      };\n\n      const onExit = () => {\n        animationStep.current = AnimationStep.Exit;\n        node.className += ` ${exitClassName}`;\n        node.addEventListener('animationend', onExited);\n      };\n\n      if (!isIn) preventExitTransition ? onExited() : onExit();\n    }, [isIn]);\n\n    return <>{children}</>;\n  };\n}\n", "import { Default } from './constant';\n\n/**\n * Used to collapse toast after exit animation\n */\nexport function collapseToast(node: HTMLElement, done: () => void, duration = Default.COLLAPSE_DURATION) {\n  const { scrollHeight, style } = node;\n\n  requestAnimationFrame(() => {\n    style.minHeight = 'initial';\n    style.height = scrollHeight + 'px';\n    style.transition = `all ${duration}ms`;\n\n    requestAnimationFrame(() => {\n      style.height = '0';\n      style.padding = '0';\n      style.margin = '0';\n      setTimeout(done, duration as number);\n    });\n  });\n}\n", "import { Toast, ToastContentProps, ToastItem, ToastItemStatus, ToastProps } from '../types';\nimport { cloneElement, isValidElement, ReactElement } from 'react';\nimport { isFn, isStr } from './propValidator';\n\nexport function toToastItem(toast: Toast, status: ToastItemStatus): ToastItem {\n  return {\n    content: renderContent(toast.content, toast.props),\n    containerId: toast.props.containerId,\n    id: toast.props.toastId,\n    theme: toast.props.theme,\n    type: toast.props.type,\n    data: toast.props.data || {},\n    isLoading: toast.props.isLoading,\n    icon: toast.props.icon,\n    reason: toast.removalReason,\n    status\n  };\n}\n\nexport function renderContent(content: unknown, props: ToastProps, isPaused: boolean = false) {\n  if (isValidElement(content) && !isStr(content.type)) {\n    return cloneElement<ToastContentProps>(content as ReactElement<any>, {\n      closeToast: props.closeToast,\n      toastProps: props,\n      data: props.data,\n      isPaused\n    });\n  } else if (isFn(content)) {\n    return content({\n      closeToast: props.closeToast,\n      toastProps: props,\n      data: props.data,\n      isPaused\n    });\n  }\n\n  return content;\n}\n", "import React from 'react';\nimport { Default } from '../utils';\nimport { CloseToastFunc, Theme, TypeOptions } from '../types';\n\nexport interface CloseButtonProps {\n  closeToast: CloseToastFunc;\n  type: TypeOptions;\n  ariaLabel?: string;\n  theme: Theme;\n}\n\nexport function CloseButton({ closeToast, theme, ariaLabel = 'close' }: CloseButtonProps) {\n  return (\n    <button\n      className={`${Default.CSS_NAMESPACE}__close-button ${Default.CSS_NAMESPACE}__close-button--${theme}`}\n      type=\"button\"\n      onClick={e => {\n        e.stopPropagation();\n        closeToast(true);\n      }}\n      aria-label={ariaLabel}\n    >\n      <svg aria-hidden=\"true\" viewBox=\"0 0 14 16\">\n        <path\n          fillRule=\"evenodd\"\n          d=\"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z\"\n        />\n      </svg>\n    </button>\n  );\n}\n", "import React from 'react';\nimport cx from 'clsx';\n\nimport { Default, isFn, Type } from '../utils';\nimport { Theme, ToastClassName, TypeOptions } from '../types';\n\nexport interface ProgressBarProps {\n  /**\n   * The animation delay which determine when to close the toast\n   */\n  delay: number;\n\n  /**\n   * The animation is running or paused\n   */\n  isRunning: boolean;\n\n  /**\n   * Func to close the current toast\n   */\n  closeToast: () => void;\n\n  /**\n   * Optional type : info, success ...\n   */\n  type?: TypeOptions;\n\n  /**\n   * The theme that is currently used\n   */\n  theme: Theme;\n\n  /**\n   * Hide or not the progress bar\n   */\n  hide?: boolean;\n\n  /**\n   * Optional className\n   */\n  className?: ToastClassName;\n\n  /**\n   * Tell whether a controlled progress bar is used\n   */\n  controlledProgress?: boolean;\n\n  /**\n   * Controlled progress value\n   */\n  progress?: number | string;\n\n  /**\n   * Support rtl content\n   */\n  rtl?: boolean;\n\n  /**\n   * Tell if the component is visible on screen or not\n   */\n  isIn?: boolean;\n}\n\nexport function ProgressBar({\n  delay,\n  isRunning,\n  closeToast,\n  type = Type.DEFAULT,\n  hide,\n  className,\n  controlledProgress,\n  progress,\n  rtl,\n  isIn,\n  theme\n}: ProgressBarProps) {\n  const isHidden = hide || (controlledProgress && progress === 0);\n  const style: React.CSSProperties = {\n    animationDuration: `${delay}ms`,\n    animationPlayState: isRunning ? 'running' : 'paused'\n  };\n\n  if (controlledProgress) style.transform = `scaleX(${progress})`;\n  const defaultClassName = cx(\n    `${Default.CSS_NAMESPACE}__progress-bar`,\n    controlledProgress\n      ? `${Default.CSS_NAMESPACE}__progress-bar--controlled`\n      : `${Default.CSS_NAMESPACE}__progress-bar--animated`,\n    `${Default.CSS_NAMESPACE}__progress-bar-theme--${theme}`,\n    `${Default.CSS_NAMESPACE}__progress-bar--${type}`,\n    {\n      [`${Default.CSS_NAMESPACE}__progress-bar--rtl`]: rtl\n    }\n  );\n  const classNames = isFn(className)\n    ? className({\n        rtl,\n        type,\n        defaultClassName\n      })\n    : cx(defaultClassName, className);\n\n  // 🧐 controlledProgress is derived from progress\n  // so if controlledProgress is set\n  // it means that this is also the case for progress\n  const animationEvent = {\n    [controlledProgress && (progress as number)! >= 1 ? 'onTransitionEnd' : 'onAnimationEnd']:\n      controlledProgress && (progress as number)! < 1\n        ? null\n        : () => {\n            isIn && closeToast();\n          }\n  };\n\n  // TODO: add aria-valuenow, aria-valuemax, aria-valuemin\n\n  return (\n    <div className={`${Default.CSS_NAMESPACE}__progress-bar--wrp`} data-hidden={isHidden}>\n      <div\n        className={`${Default.CSS_NAMESPACE}__progress-bar--bg ${Default.CSS_NAMESPACE}__progress-bar-theme--${theme} ${Default.CSS_NAMESPACE}__progress-bar--${type}`}\n      />\n      <div\n        role=\"progressbar\"\n        aria-hidden={isHidden ? 'true' : 'false'}\n        aria-label=\"notification timer\"\n        className={classNames}\n        style={style}\n        {...animationEvent}\n      />\n    </div>\n  );\n}\n", "import cx from 'clsx';\nimport React, { useEffect, useRef, useState } from 'react';\n\nimport { toast } from '../core';\nimport { useToastContainer } from '../hooks';\nimport { useIsomorphicLayoutEffect } from '../hooks/useIsomorphicLayoutEffect';\nimport { ToastContainerProps, ToastPosition } from '../types';\nimport { Default, Direction, isFn, parseClassName } from '../utils';\nimport { Toast } from './Toast';\nimport { Bounce } from './Transitions';\n\nexport const defaultProps: ToastContainerProps = {\n  position: 'top-right',\n  transition: Bounce,\n  autoClose: 5000,\n  closeButton: true,\n  pauseOnHover: true,\n  pauseOnFocusLoss: true,\n  draggable: 'touch',\n  draggablePercent: Default.DRAGGABLE_PERCENT as number,\n  draggableDirection: Direction.X,\n  role: 'alert',\n  theme: 'light',\n  'aria-label': 'Notifications Alt+T',\n  hotKeys: e => e.altKey && e.code === 'KeyT'\n};\n\nexport function ToastContainer(props: ToastContainerProps) {\n  let containerProps: ToastContainerProps = {\n    ...defaultProps,\n    ...props\n  };\n  const stacked = props.stacked;\n  const [collapsed, setIsCollapsed] = useState(true);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const { getToastToRender, isToastActive, count } = useToastContainer(containerProps);\n  const { className, style, rtl, containerId, hotKeys } = containerProps;\n\n  function getClassName(position: ToastPosition) {\n    const defaultClassName = cx(\n      `${Default.CSS_NAMESPACE}__toast-container`,\n      `${Default.CSS_NAMESPACE}__toast-container--${position}`,\n      { [`${Default.CSS_NAMESPACE}__toast-container--rtl`]: rtl }\n    );\n    return isFn(className)\n      ? className({\n          position,\n          rtl,\n          defaultClassName\n        })\n      : cx(defaultClassName, parseClassName(className));\n  }\n\n  function collapseAll() {\n    if (stacked) {\n      setIsCollapsed(true);\n      toast.play();\n    }\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (stacked) {\n      const nodes = containerRef.current!.querySelectorAll('[data-in=\"true\"]');\n      const gap = 12;\n      const isTop = containerProps.position?.includes('top');\n      let usedHeight = 0;\n      let prevS = 0;\n\n      Array.from(nodes)\n        .reverse()\n        .forEach((n, i) => {\n          const node = n as HTMLElement;\n          node.classList.add(`${Default.CSS_NAMESPACE}__toast--stacked`);\n\n          if (i > 0) node.dataset.collapsed = `${collapsed}`;\n\n          if (!node.dataset.pos) node.dataset.pos = isTop ? 'top' : 'bot';\n\n          const y = usedHeight * (collapsed ? 0.2 : 1) + (collapsed ? 0 : gap * i);\n\n          node.style.setProperty('--y', `${isTop ? y : y * -1}px`);\n          node.style.setProperty('--g', `${gap}`);\n          node.style.setProperty('--s', `${1 - (collapsed ? prevS : 0)}`);\n\n          usedHeight += node.offsetHeight;\n          prevS += 0.025;\n        });\n    }\n  }, [collapsed, count, stacked]);\n\n  useEffect(() => {\n    function focusFirst(e: KeyboardEvent) {\n      const node = containerRef.current;\n      if (hotKeys(e)) {\n        (node.querySelector('[tabIndex=\"0\"]') as HTMLElement)?.focus();\n        setIsCollapsed(false);\n        toast.pause();\n      }\n      if (e.key === 'Escape' && (document.activeElement === node || node?.contains(document.activeElement))) {\n        setIsCollapsed(true);\n        toast.play();\n      }\n    }\n\n    document.addEventListener('keydown', focusFirst);\n\n    return () => {\n      document.removeEventListener('keydown', focusFirst);\n    };\n  }, [hotKeys]);\n\n  return (\n    <section\n      ref={containerRef}\n      className={Default.CSS_NAMESPACE as string}\n      id={containerId as string}\n      onMouseEnter={() => {\n        if (stacked) {\n          setIsCollapsed(false);\n          toast.pause();\n        }\n      }}\n      onMouseLeave={collapseAll}\n      aria-live=\"polite\"\n      aria-atomic=\"false\"\n      aria-relevant=\"additions text\"\n      aria-label={containerProps['aria-label']}\n    >\n      {getToastToRender((position, toastList) => {\n        const containerStyle: React.CSSProperties = !toastList.length\n          ? { ...style, pointerEvents: 'none' }\n          : { ...style };\n\n        return (\n          <div\n            tabIndex={-1}\n            className={getClassName(position)}\n            data-stacked={stacked}\n            style={containerStyle}\n            key={`c-${position}`}\n          >\n            {toastList.map(({ content, props: toastProps }) => {\n              return (\n                <Toast\n                  {...toastProps}\n                  stacked={stacked}\n                  collapseAll={collapseAll}\n                  isIn={isToastActive(toastProps.toastId, toastProps.containerId)}\n                  key={`t-${toastProps.key}`}\n                >\n                  {content}\n                </Toast>\n              );\n            })}\n          </div>\n        );\n      })}\n    </section>\n  );\n}\n", "let TOAST_ID = 1;\n\nexport const genToastId = () => `${TOAST_ID++}`;\n", "import {\n  Id,\n  NotValidatedToastProps,\n  OnChangeCallback,\n  Toast,\n  ToastContainerProps,\n  ToastContent,\n  ToastProps\n} from '../types';\nimport { canBeRendered, getAutoCloseDelay, isNum, parseClassName, toToastItem } from '../utils';\n\ntype Notify = () => void;\n\nexport type ContainerObserver = ReturnType<typeof createContainerObserver>;\n\nexport function createContainerObserver(\n  id: Id,\n  containerProps: ToastContainerProps,\n  dispatchChanges: OnChangeCallback\n) {\n  let toastKey = 1;\n  let toastCount = 0;\n  let queue: Toast[] = [];\n  let snapshot: Toast[] = [];\n  let props = containerProps;\n  const toasts = new Map<Id, Toast>();\n  const listeners = new Set<Notify>();\n\n  const observe = (notify: Notify) => {\n    listeners.add(notify);\n    return () => listeners.delete(notify);\n  };\n\n  const notify = () => {\n    snapshot = Array.from(toasts.values());\n    listeners.forEach(cb => cb());\n  };\n\n  const shouldIgnoreToast = ({ containerId, toastId, updateId }: NotValidatedToastProps) => {\n    const containerMismatch = containerId ? containerId !== id : id !== 1;\n    const isDuplicate = toasts.has(toastId) && updateId == null;\n\n    return containerMismatch || isDuplicate;\n  };\n\n  const toggle = (v: boolean, id?: Id) => {\n    toasts.forEach(t => {\n      if (id == null || id === t.props.toastId) t.toggle?.(v);\n    });\n  };\n\n  const markAsRemoved = (v: Toast) => {\n    v.props?.onClose?.(v.removalReason);\n    v.isActive = false;\n  };\n\n  const removeToast = (id?: Id) => {\n    if (id == null) {\n      toasts.forEach(markAsRemoved);\n    } else {\n      const t = toasts.get(id);\n      if (t) markAsRemoved(t);\n    }\n    notify();\n  };\n\n  const clearQueue = () => {\n    toastCount -= queue.length;\n    queue = [];\n  };\n\n  const addActiveToast = (toast: Toast) => {\n    const { toastId, updateId } = toast.props;\n    const isNew = updateId == null;\n\n    if (toast.staleId) toasts.delete(toast.staleId);\n    toast.isActive = true;\n\n    toasts.set(toastId, toast);\n    notify();\n    dispatchChanges(toToastItem(toast, isNew ? 'added' : 'updated'));\n\n    if (isNew) toast.props.onOpen?.();\n  };\n\n  const buildToast = <TData = unknown>(content: ToastContent<TData>, options: NotValidatedToastProps) => {\n    if (shouldIgnoreToast(options)) return;\n\n    const { toastId, updateId, data, staleId, delay } = options;\n\n    const isNotAnUpdate = updateId == null;\n\n    if (isNotAnUpdate) toastCount++;\n\n    const toastProps = {\n      ...props,\n      style: props.toastStyle,\n      key: toastKey++,\n      ...Object.fromEntries(Object.entries(options).filter(([_, v]) => v != null)),\n      toastId,\n      updateId,\n      data,\n      isIn: false,\n      className: parseClassName(options.className || props.toastClassName),\n      progressClassName: parseClassName(options.progressClassName || props.progressClassName),\n      autoClose: options.isLoading ? false : getAutoCloseDelay(options.autoClose, props.autoClose),\n      closeToast(reason?: true) {\n        toasts.get(toastId)!.removalReason = reason;\n        removeToast(toastId);\n      },\n      deleteToast() {\n        const toastToRemove = toasts.get(toastId);\n\n        if (toastToRemove == null) return;\n\n        dispatchChanges(toToastItem(toastToRemove, 'removed'));\n        toasts.delete(toastId);\n\n        toastCount--;\n        if (toastCount < 0) toastCount = 0;\n\n        if (queue.length > 0) {\n          addActiveToast(queue.shift());\n          return;\n        }\n\n        notify();\n      }\n    } as ToastProps;\n\n    toastProps.closeButton = props.closeButton;\n\n    if (options.closeButton === false || canBeRendered(options.closeButton)) {\n      toastProps.closeButton = options.closeButton;\n    } else if (options.closeButton === true) {\n      toastProps.closeButton = canBeRendered(props.closeButton) ? props.closeButton : true;\n    }\n\n    const activeToast = {\n      content,\n      props: toastProps,\n      staleId\n    } as Toast;\n\n    // not handling limit + delay by design. Waiting for user feedback first\n    if (props.limit && props.limit > 0 && toastCount > props.limit && isNotAnUpdate) {\n      queue.push(activeToast);\n    } else if (isNum(delay)) {\n      setTimeout(() => {\n        addActiveToast(activeToast);\n      }, delay);\n    } else {\n      addActiveToast(activeToast);\n    }\n  };\n\n  return {\n    id,\n    props,\n    observe,\n    toggle,\n    removeToast,\n    toasts,\n    clearQueue,\n    buildToast,\n    setProps(p: ToastContainerProps) {\n      props = p;\n    },\n    setToggle: (id: Id, fn: (v: boolean) => void) => {\n      const t = toasts.get(id);\n      if (t) t.toggle = fn;\n    },\n    isToastActive: (id: Id) => toasts.get(id)?.isActive,\n    getSnapshot: () => snapshot\n  };\n}\n", "import {\n  ClearWaitingQueueParams,\n  Id,\n  NotValidatedToastProps,\n  OnChangeCallback,\n  ToastContainerProps,\n  ToastContent,\n  ToastItem,\n  ToastOptions\n} from '../types';\nimport { Default, canBeRendered, isId } from '../utils';\nimport { ContainerObserver, createContainerObserver } from './containerObserver';\n\ninterface EnqueuedToast {\n  content: ToastContent<any>;\n  options: NotValidatedToastProps;\n}\n\ninterface RemoveParams {\n  id?: Id;\n  containerId: Id;\n}\n\nconst containers = new Map<Id, ContainerObserver>();\nlet renderQueue: EnqueuedToast[] = [];\nconst listeners = new Set<OnChangeCallback>();\n\nconst dispatchChanges = (data: ToastItem) => listeners.forEach(cb => cb(data));\n\nconst hasContainers = () => containers.size > 0;\n\nfunction flushRenderQueue() {\n  renderQueue.forEach(v => pushToast(v.content, v.options));\n  renderQueue = [];\n}\n\nexport const getToast = (id: Id, { containerId }: ToastOptions) =>\n  containers.get(containerId || Default.CONTAINER_ID)?.toasts.get(id);\n\nexport function isToastActive(id: Id, containerId?: Id) {\n  if (containerId) return !!containers.get(containerId)?.isToastActive(id);\n\n  let isActive = false;\n  containers.forEach(c => {\n    if (c.isToastActive(id)) isActive = true;\n  });\n\n  return isActive;\n}\n\nexport function removeToast(params?: Id | RemoveParams) {\n  if (!hasContainers()) {\n    renderQueue = renderQueue.filter(v => params != null && v.options.toastId !== params);\n    return;\n  }\n\n  if (params == null || isId(params)) {\n    containers.forEach(c => {\n      c.removeToast(params as Id);\n    });\n  } else if (params && ('containerId' in params || 'id' in params)) {\n    const container = containers.get(params.containerId);\n    container\n      ? container.removeToast(params.id)\n      : containers.forEach(c => {\n          c.removeToast(params.id);\n        });\n  }\n}\n\nexport const clearWaitingQueue = (p: ClearWaitingQueueParams = {}) => {\n  containers.forEach(c => {\n    if (c.props.limit && (!p.containerId || c.id === p.containerId)) {\n      c.clearQueue();\n    }\n  });\n};\n\nexport function pushToast<TData>(content: ToastContent<TData>, options: NotValidatedToastProps) {\n  if (!canBeRendered(content)) return;\n  if (!hasContainers()) renderQueue.push({ content, options });\n\n  containers.forEach(c => {\n    c.buildToast(content, options);\n  });\n}\n\ninterface ToggleToastParams {\n  id?: Id;\n  containerId?: Id;\n}\n\ntype RegisterToggleOpts = {\n  id: Id;\n  containerId?: Id;\n  fn: (v: boolean) => void;\n};\n\nexport function registerToggle(opts: RegisterToggleOpts) {\n  containers.get(opts.containerId || Default.CONTAINER_ID)?.setToggle(opts.id, opts.fn);\n}\n\nexport function toggleToast(v: boolean, opt?: ToggleToastParams) {\n  containers.forEach(c => {\n    if (opt == null || !opt?.containerId) {\n      c.toggle(v, opt?.id);\n    } else if (opt?.containerId === c.id) {\n      c.toggle(v, opt?.id);\n    }\n  });\n}\n\nexport function registerContainer(props: ToastContainerProps) {\n  const id = props.containerId || Default.CONTAINER_ID;\n  return {\n    subscribe(notify: () => void) {\n      const container = createContainerObserver(id, props, dispatchChanges);\n\n      containers.set(id, container);\n      const unobserve = container.observe(notify);\n      flushRenderQueue();\n\n      return () => {\n        unobserve();\n        containers.delete(id);\n      };\n    },\n    setProps(p: ToastContainerProps) {\n      containers.get(id)?.setProps(p);\n    },\n    getSnapshot() {\n      return containers.get(id)?.getSnapshot();\n    }\n  };\n}\n\nexport function onChange(cb: OnChangeCallback) {\n  listeners.add(cb);\n\n  return () => {\n    listeners.delete(cb);\n  };\n}\n", "import {\n  ClearWaitingQueueFunc,\n  Id,\n  IdOpts,\n  NotValidatedToastProps,\n  OnChangeCallback,\n  ToastContent,\n  ToastOptions,\n  ToastProps,\n  TypeOptions,\n  UpdateOptions\n} from '../types';\nimport { isFn, isNum, isStr, Type } from '../utils';\nimport { genToastId } from './genToastId';\nimport { clearWaitingQueue, getToast, isToastActive, onChange, pushToast, removeToast, toggleToast } from './store';\n\n/**\n * Generate a toastId or use the one provided\n */\nfunction getToastId<TData>(options?: ToastOptions<TData>) {\n  return options && (isStr(options.toastId) || isNum(options.toastId)) ? options.toastId : genToastId();\n}\n\n/**\n * If the container is not mounted, the toast is enqueued\n */\nfunction dispatchToast<TData>(content: ToastContent<TData>, options: NotValidatedToastProps): Id {\n  pushToast(content, options);\n  return options.toastId;\n}\n\n/**\n * Merge provided options with the defaults settings and generate the toastId\n */\nfunction mergeOptions<TData>(type: string, options?: ToastOptions<TData>) {\n  return {\n    ...options,\n    type: (options && options.type) || type,\n    toastId: getToastId(options)\n  } as NotValidatedToastProps;\n}\n\nfunction createToastByType(type: string) {\n  return <TData = unknown>(content: ToastContent<TData>, options?: ToastOptions<TData>) =>\n    dispatchToast(content, mergeOptions(type, options));\n}\n\nfunction toast<TData = unknown>(content: ToastContent<TData>, options?: ToastOptions<TData>) {\n  return dispatchToast(content, mergeOptions(Type.DEFAULT, options));\n}\n\ntoast.loading = <TData = unknown>(content: ToastContent<TData>, options?: ToastOptions<TData>) =>\n  dispatchToast(\n    content,\n    mergeOptions(Type.DEFAULT, {\n      isLoading: true,\n      autoClose: false,\n      closeOnClick: false,\n      closeButton: false,\n      draggable: false,\n      ...options\n    })\n  );\n\nexport interface ToastPromiseParams<TData = unknown, TError = unknown, TPending = unknown> {\n  pending?: string | UpdateOptions<TPending>;\n  success?: string | UpdateOptions<TData>;\n  error?: string | UpdateOptions<TError>;\n}\n\nfunction handlePromise<TData = unknown, TError = unknown, TPending = unknown>(\n  promise: Promise<TData> | (() => Promise<TData>),\n  { pending, error, success }: ToastPromiseParams<TData, TError, TPending>,\n  options?: ToastOptions<TData>\n) {\n  let id: Id;\n\n  if (pending) {\n    id = isStr(pending)\n      ? toast.loading(pending, options)\n      : toast.loading(pending.render, {\n          ...options,\n          ...(pending as ToastOptions)\n        } as ToastOptions<TPending>);\n  }\n\n  const resetParams = {\n    isLoading: null,\n    autoClose: null,\n    closeOnClick: null,\n    closeButton: null,\n    draggable: null\n  };\n\n  const resolver = <T>(type: TypeOptions, input: string | UpdateOptions<T> | undefined, result: T) => {\n    // Remove the toast if the input has not been provided. This prevents the toast from hanging\n    // in the pending state if a success/error toast has not been provided.\n    if (input == null) {\n      toast.dismiss(id);\n      return;\n    }\n\n    const baseParams = {\n      type,\n      ...resetParams,\n      ...options,\n      data: result\n    };\n    const params = isStr(input) ? { render: input } : input;\n\n    // if the id is set we know that it's an update\n    if (id) {\n      toast.update(id, {\n        ...baseParams,\n        ...params\n      } as UpdateOptions);\n    } else {\n      // using toast.promise without loading\n      toast(params!.render, {\n        ...baseParams,\n        ...params\n      } as ToastOptions<T>);\n    }\n\n    return result;\n  };\n\n  const p = isFn(promise) ? promise() : promise;\n\n  //call the resolvers only when needed\n  p.then(result => resolver('success', success, result)).catch(err => resolver('error', error, err));\n\n  return p;\n}\n\n/**\n * Supply a promise or a function that return a promise and the notification will be updated if it resolves or fails.\n * When the promise is pending a spinner is displayed by default.\n * `toast.promise` returns the provided promise so you can chain it.\n *\n * Simple example:\n *\n * ```\n * toast.promise(MyPromise,\n *  {\n *    pending: 'Promise is pending',\n *    success: 'Promise resolved 👌',\n *    error: 'Promise rejected 🤯'\n *  }\n * )\n *\n * ```\n *\n * Advanced usage:\n * ```\n * toast.promise<{name: string}, {message: string}, undefined>(\n *    resolveWithSomeData,\n *    {\n *      pending: {\n *        render: () => \"I'm loading\",\n *        icon: false,\n *      },\n *      success: {\n *        render: ({data}) => `Hello ${data.name}`,\n *        icon: \"🟢\",\n *      },\n *      error: {\n *        render({data}){\n *          // When the promise reject, data will contains the error\n *          return <MyErrorComponent message={data.message} />\n *        }\n *      }\n *    }\n * )\n * ```\n */\ntoast.promise = handlePromise;\ntoast.success = createToastByType(Type.SUCCESS);\ntoast.info = createToastByType(Type.INFO);\ntoast.error = createToastByType(Type.ERROR);\ntoast.warning = createToastByType(Type.WARNING);\ntoast.warn = toast.warning;\ntoast.dark = (content: ToastContent, options?: ToastOptions) =>\n  dispatchToast(\n    content,\n    mergeOptions(Type.DEFAULT, {\n      theme: 'dark',\n      ...options\n    })\n  );\n\ninterface RemoveParams {\n  id?: Id;\n  containerId: Id;\n}\n\nfunction dismiss(params: RemoveParams): void;\nfunction dismiss(params?: Id): void;\nfunction dismiss(params?: Id | RemoveParams) {\n  removeToast(params);\n}\n\n/**\n * Remove toast programmatically\n *\n * - Remove all toasts:\n * ```\n * toast.dismiss()\n * ```\n *\n * - Remove all toasts that belongs to a given container\n * ```\n * toast.dismiss({ container: \"123\" })\n * ```\n *\n * - Remove toast that has a given id regardless the container\n * ```\n * toast.dismiss({ id: \"123\" })\n * ```\n *\n * - Remove toast that has a given id for a specific container\n * ```\n * toast.dismiss({ id: \"123\", containerId: \"12\" })\n * ```\n */\ntoast.dismiss = dismiss;\n\n/**\n * Clear waiting queue when limit is used\n */\ntoast.clearWaitingQueue = clearWaitingQueue as ClearWaitingQueueFunc;\n\n/**\n * Check if a toast is active\n *\n * - Check regardless the container\n * ```\n * toast.isActive(\"123\")\n * ```\n *\n * - Check in a specific container\n * ```\n * toast.isActive(\"123\", \"containerId\")\n * ```\n */\ntoast.isActive = isToastActive;\n\n/**\n * Update a toast, see https://fkhadra.github.io/react-toastify/update-toast/ for more\n *\n * Example:\n * ```\n * // With a string\n * toast.update(toastId, {\n *    render: \"New content\",\n *    type: \"info\",\n * });\n *\n * // Or with a component\n * toast.update(toastId, {\n *    render: MyComponent\n * });\n *\n * // Or a function\n * toast.update(toastId, {\n *    render: () => <div>New content</div>\n * });\n *\n * // Apply a transition\n * toast.update(toastId, {\n *   render: \"New Content\",\n *   type: toast.TYPE.INFO,\n *   transition: Rotate\n * })\n * ```\n */\ntoast.update = <TData = unknown>(toastId: Id, options: UpdateOptions<TData> = {}) => {\n  const toast = getToast(toastId, options as ToastOptions);\n\n  if (toast) {\n    const { props: oldOptions, content: oldContent } = toast;\n\n    const nextOptions = {\n      delay: 100,\n      ...oldOptions,\n      ...options,\n      toastId: options.toastId || toastId,\n      updateId: genToastId()\n    } as ToastProps & UpdateOptions;\n\n    if (nextOptions.toastId !== toastId) nextOptions.staleId = toastId;\n\n    const content = nextOptions.render || oldContent;\n    delete nextOptions.render;\n\n    dispatchToast(content, nextOptions);\n  }\n};\n\n/**\n * Used for controlled progress bar. It will automatically close the notification.\n *\n * If you don't want your notification to be clsoed when the timer is done you should use `toast.update` instead as follow instead:\n *\n * ```\n * toast.update(id, {\n *    progress: null, // remove controlled progress bar\n *    render: \"ok\",\n *    type: \"success\",\n *    autoClose: 5000 // set autoClose to the desired value\n *   });\n * ```\n */\ntoast.done = (id: Id) => {\n  toast.update(id, {\n    progress: 1\n  });\n};\n\n/**\n * Subscribe to change when a toast is added, removed and updated\n *\n * Usage:\n * ```\n * const unsubscribe = toast.onChange((payload) => {\n *   switch (payload.status) {\n *   case \"added\":\n *     // new toast added\n *     break;\n *   case \"updated\":\n *     // toast updated\n *     break;\n *   case \"removed\":\n *     // toast has been removed\n *     break;\n *   }\n * })\n * ```\n */\ntoast.onChange = onChange as (cb: OnChangeCallback) => () => void;\n\n/**\n * Play a toast(s) timer progammatically\n *\n * Usage:\n *\n * - Play all toasts\n * ```\n * toast.play()\n * ```\n *\n * - Play all toasts for a given container\n * ```\n * toast.play({ containerId: \"123\" })\n * ```\n *\n * - Play toast that has a given id regardless the container\n * ```\n * toast.play({ id: \"123\" })\n * ```\n *\n * - Play toast that has a given id for a specific container\n * ```\n * toast.play({ id: \"123\", containerId: \"12\" })\n * ```\n */\ntoast.play = (opts?: IdOpts) => toggleToast(true, opts);\n\n/**\n * Pause a toast(s) timer progammatically\n *\n * Usage:\n *\n * - Pause all toasts\n * ```\n * toast.pause()\n * ```\n *\n * - Pause all toasts for a given container\n * ```\n * toast.pause({ containerId: \"123\" })\n * ```\n *\n * - Pause toast that has a given id regardless the container\n * ```\n * toast.pause({ id: \"123\" })\n * ```\n *\n * - Pause toast that has a given id for a specific container\n * ```\n * toast.pause({ id: \"123\", containerId: \"12\" })\n * ```\n */\ntoast.pause = (opts?: IdOpts) => toggleToast(false, opts);\n\nexport { toast };\n", "import { useRef, useSyncExternalStore } from 'react';\nimport { isToastActive, registerContainer } from '../core/store';\nimport { Toast, ToastContainerProps, ToastPosition } from '../types';\n\nexport function useToastContainer(props: ToastContainerProps) {\n  const { subscribe, getSnapshot, setProps } = useRef(registerContainer(props)).current;\n  setProps(props);\n  const snapshot = useSyncExternalStore(subscribe, getSnapshot, getSnapshot)?.slice();\n\n  function getToastToRender<T>(cb: (position: ToastPosition, toastList: Toast[]) => T) {\n    if (!snapshot) return [];\n\n    const toRender = new Map<ToastPosition, Toast[]>();\n\n    if (props.newestOnTop) snapshot.reverse();\n\n    snapshot.forEach(toast => {\n      const { position } = toast.props;\n      toRender.has(position) || toRender.set(position, []);\n      toRender.get(position)!.push(toast);\n    });\n\n    return Array.from(toRender, p => cb(p[0], p[1]));\n  }\n\n  return {\n    getToastToRender,\n    isToastActive,\n    count: snapshot?.length\n  };\n}\n", "import { DOMAttributes, useEffect, useRef, useState } from 'react';\n\nimport { ToastProps } from '../types';\nimport { Default, Direction } from '../utils';\nimport { registerToggle } from '../core/store';\n\ninterface Draggable {\n  start: number;\n  delta: number;\n  removalDistance: number;\n  canCloseOnClick: boolean;\n  canDrag: boolean;\n  didMove: boolean;\n}\n\nexport function useToast(props: ToastProps) {\n  const [isRunning, setIsRunning] = useState(false);\n  const [preventExitTransition, setPreventExitTransition] = useState(false);\n  const toastRef = useRef<HTMLDivElement>(null);\n  const drag = useRef<Draggable>({\n    start: 0,\n    delta: 0,\n    removalDistance: 0,\n    canCloseOnClick: true,\n    canDrag: false,\n    didMove: false\n  }).current;\n  const { autoClose, pauseOnHover, closeToast, onClick, closeOnClick } = props;\n\n  registerToggle({\n    id: props.toastId,\n    containerId: props.containerId,\n    fn: setIsRunning\n  });\n\n  useEffect(() => {\n    if (props.pauseOnFocusLoss) {\n      bindFocusEvents();\n\n      return () => {\n        unbindFocusEvents();\n      };\n    }\n  }, [props.pauseOnFocusLoss]);\n\n  function bindFocusEvents() {\n    if (!document.hasFocus()) pauseToast();\n\n    window.addEventListener('focus', playToast);\n    window.addEventListener('blur', pauseToast);\n  }\n\n  function unbindFocusEvents() {\n    window.removeEventListener('focus', playToast);\n    window.removeEventListener('blur', pauseToast);\n  }\n\n  function onDragStart(e: React.PointerEvent<HTMLElement>) {\n    if (props.draggable === true || props.draggable === e.pointerType) {\n      bindDragEvents();\n      const toast = toastRef.current!;\n      drag.canCloseOnClick = true;\n      drag.canDrag = true;\n      toast.style.transition = 'none';\n\n      if (props.draggableDirection === Direction.X) {\n        drag.start = e.clientX;\n        drag.removalDistance = toast.offsetWidth * (props.draggablePercent / 100);\n      } else {\n        drag.start = e.clientY;\n        drag.removalDistance =\n          (toast.offsetHeight *\n            (props.draggablePercent === Default.DRAGGABLE_PERCENT\n              ? props.draggablePercent * 1.5\n              : props.draggablePercent)) /\n          100;\n      }\n    }\n  }\n\n  function onDragTransitionEnd(e: React.PointerEvent<HTMLElement>) {\n    const { top, bottom, left, right } = toastRef.current!.getBoundingClientRect();\n\n    if (\n      e.nativeEvent.type !== 'touchend' &&\n      props.pauseOnHover &&\n      e.clientX >= left &&\n      e.clientX <= right &&\n      e.clientY >= top &&\n      e.clientY <= bottom\n    ) {\n      pauseToast();\n    } else {\n      playToast();\n    }\n  }\n\n  function playToast() {\n    setIsRunning(true);\n  }\n\n  function pauseToast() {\n    setIsRunning(false);\n  }\n\n  function bindDragEvents() {\n    drag.didMove = false;\n    document.addEventListener('pointermove', onDragMove);\n    document.addEventListener('pointerup', onDragEnd);\n  }\n\n  function unbindDragEvents() {\n    document.removeEventListener('pointermove', onDragMove);\n    document.removeEventListener('pointerup', onDragEnd);\n  }\n\n  function onDragMove(e: PointerEvent) {\n    const toast = toastRef.current!;\n    if (drag.canDrag && toast) {\n      drag.didMove = true;\n      if (isRunning) pauseToast();\n      if (props.draggableDirection === Direction.X) {\n        drag.delta = e.clientX - drag.start;\n      } else {\n        drag.delta = e.clientY - drag.start;\n      }\n\n      // prevent false positive during a toast click\n      if (drag.start !== e.clientX) drag.canCloseOnClick = false;\n      const translate =\n        props.draggableDirection === 'x' ? `${drag.delta}px, var(--y)` : `0, calc(${drag.delta}px + var(--y))`;\n      toast.style.transform = `translate3d(${translate},0)`;\n      toast.style.opacity = `${1 - Math.abs(drag.delta / drag.removalDistance)}`;\n    }\n  }\n\n  function onDragEnd() {\n    unbindDragEvents();\n    const toast = toastRef.current!;\n    if (drag.canDrag && drag.didMove && toast) {\n      drag.canDrag = false;\n      if (Math.abs(drag.delta) > drag.removalDistance) {\n        setPreventExitTransition(true);\n        props.closeToast(true);\n        props.collapseAll();\n        return;\n      }\n\n      toast.style.transition = 'transform 0.2s, opacity 0.2s';\n      toast.style.removeProperty('transform');\n      toast.style.removeProperty('opacity');\n    }\n  }\n\n  const eventHandlers: DOMAttributes<HTMLElement> = {\n    onPointerDown: onDragStart,\n    onPointerUp: onDragTransitionEnd\n  };\n\n  if (autoClose && pauseOnHover) {\n    eventHandlers.onMouseEnter = pauseToast;\n\n    // progress control is delegated to the container\n    if (!props.stacked) eventHandlers.onMouseLeave = playToast;\n  }\n\n  // prevent toast from closing when user drags the toast\n  if (closeOnClick) {\n    eventHandlers.onClick = (e: React.MouseEvent) => {\n      onClick && onClick(e);\n      drag.canCloseOnClick && closeToast(true);\n    };\n  }\n\n  return {\n    playToast,\n    pauseToast,\n    isRunning,\n    preventExitTransition,\n    toastRef,\n    eventHandlers\n  };\n}\n", "import { useEffect, useLayoutEffect } from 'react';\n\nexport const useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\n", "import cx from 'clsx';\nimport React, { cloneElement, isValidElement } from 'react';\n\nimport { useToast } from '../hooks/useToast';\nimport { ToastProps } from '../types';\nimport { Default, isFn, renderContent } from '../utils';\nimport { CloseButton } from './CloseButton';\nimport { ProgressBar } from './ProgressBar';\nimport { getIcon } from './Icons';\n\nexport const Toast: React.FC<ToastProps> = props => {\n  const { isRunning, preventExitTransition, toastRef, eventHandlers, playToast } = useToast(props);\n  const {\n    closeButton,\n    children,\n    autoClose,\n    onClick,\n    type,\n    hideProgressBar,\n    closeToast,\n    transition: Transition,\n    position,\n    className,\n    style,\n    progressClassName,\n    updateId,\n    role,\n    progress,\n    rtl,\n    toastId,\n    deleteToast,\n    isIn,\n    isLoading,\n    closeOnClick,\n    theme,\n    ariaLabel\n  } = props;\n  const defaultClassName = cx(\n    `${Default.CSS_NAMESPACE}__toast`,\n    `${Default.CSS_NAMESPACE}__toast-theme--${theme}`,\n    `${Default.CSS_NAMESPACE}__toast--${type}`,\n    {\n      [`${Default.CSS_NAMESPACE}__toast--rtl`]: rtl\n    },\n    {\n      [`${Default.CSS_NAMESPACE}__toast--close-on-click`]: closeOnClick\n    }\n  );\n  const cssClasses = isFn(className)\n    ? className({\n        rtl,\n        position,\n        type,\n        defaultClassName\n      })\n    : cx(defaultClassName, className);\n  const icon = getIcon(props);\n  const isProgressControlled = !!progress || !autoClose;\n\n  const closeButtonProps = { closeToast, type, theme };\n  let Close: React.ReactNode = null;\n\n  if (closeButton === false) {\n    // hide\n  } else if (isFn(closeButton)) {\n    Close = closeButton(closeButtonProps);\n  } else if (isValidElement(closeButton)) {\n    Close = cloneElement(closeButton, closeButtonProps);\n  } else {\n    Close = CloseButton(closeButtonProps);\n  }\n\n  return (\n    <Transition\n      isIn={isIn}\n      done={deleteToast}\n      position={position}\n      preventExitTransition={preventExitTransition}\n      nodeRef={toastRef}\n      playToast={playToast}\n    >\n      <div\n        id={toastId as string}\n        tabIndex={0}\n        onClick={onClick}\n        data-in={isIn}\n        className={cssClasses}\n        {...eventHandlers}\n        style={style}\n        ref={toastRef}\n        {...(isIn && { role: role, 'aria-label': ariaLabel })}\n      >\n        {icon != null && (\n          <div\n            className={cx(`${Default.CSS_NAMESPACE}__toast-icon`, {\n              [`${Default.CSS_NAMESPACE}--animate-icon ${Default.CSS_NAMESPACE}__zoom-enter`]: !isLoading\n            })}\n          >\n            {icon}\n          </div>\n        )}\n        {renderContent(children, props, !isRunning)}\n        {Close}\n        {!props.customProgressBar && (\n          <ProgressBar\n            {...(updateId && !isProgressControlled ? { key: `p-${updateId}` } : {})}\n            rtl={rtl}\n            theme={theme}\n            delay={autoClose as number}\n            isRunning={isRunning}\n            isIn={isIn}\n            closeToast={closeToast}\n            hide={hideProgressBar}\n            type={type}\n            className={progressClassName}\n            controlledProgress={isProgressControlled}\n            progress={progress || 0}\n          />\n        )}\n      </div>\n    </Transition>\n  );\n};\n", "import React, { cloneElement, isValidElement } from 'react';\n\nimport { Theme, ToastProps, TypeOptions } from '../types';\nimport { Default, isFn } from '../utils';\n\n/**\n * Used when providing custom icon\n */\nexport interface IconProps {\n  theme: Theme;\n  type: TypeOptions;\n  isLoading?: boolean;\n}\n\nexport type BuiltInIconProps = React.SVGProps<SVGSVGElement> & IconProps;\n\nconst Svg: React.FC<BuiltInIconProps> = ({ theme, type, isLoading, ...rest }) => (\n  <svg\n    viewBox=\"0 0 24 24\"\n    width=\"100%\"\n    height=\"100%\"\n    fill={theme === 'colored' ? 'currentColor' : `var(--toastify-icon-color-${type})`}\n    {...rest}\n  />\n);\n\nfunction Warning(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z\" />\n    </Svg>\n  );\n}\n\nfunction Info(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z\" />\n    </Svg>\n  );\n}\n\nfunction Success(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z\" />\n    </Svg>\n  );\n}\n\nfunction Error(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z\" />\n    </Svg>\n  );\n}\n\nfunction Spinner() {\n  return <div className={`${Default.CSS_NAMESPACE}__spinner`} />;\n}\n\nexport const Icons = {\n  info: Info,\n  warning: Warning,\n  success: Success,\n  error: Error,\n  spinner: Spinner\n};\n\nconst maybeIcon = (type: string): type is keyof typeof Icons => type in Icons;\n\nexport type IconParams = Pick<ToastProps, 'theme' | 'icon' | 'type' | 'isLoading'>;\n\nexport function getIcon({ theme, type, isLoading, icon }: IconParams) {\n  let Icon: React.ReactNode = null;\n  const iconProps = { theme, type };\n\n  if (icon === false) {\n    // hide\n  } else if (isFn(icon)) {\n    Icon = icon({ ...iconProps, isLoading });\n  } else if (isValidElement(icon)) {\n    Icon = cloneElement(icon, iconProps);\n  } else if (isLoading) {\n    Icon = Icons.spinner();\n  } else if (maybeIcon(type)) {\n    Icon = Icons[type](iconProps);\n  }\n\n  return Icon;\n}\n", "import { cssTransition, Default } from '../utils';\n\nconst getConfig = (animationName: string, appendPosition = false) => ({\n  enter: `${Default.CSS_NAMESPACE}--animate ${Default.CSS_NAMESPACE}__${animationName}-enter`,\n  exit: `${Default.CSS_NAMESPACE}--animate ${Default.CSS_NAMESPACE}__${animationName}-exit`,\n  appendPosition\n});\n\nconst Bounce = cssTransition(getConfig('bounce', true));\n\nconst Slide = cssTransition(getConfig('slide', true));\n\nconst Zoom = cssTransition(getConfig('zoom'));\n\nconst Flip = cssTransition(getConfig('flip'));\n\nexport { Bounce, Slide, Zoom, Flip };\n"], "mappings": ";AAAA,OAAS,kBAAAA,OAAsB,QAGxB,IAAMC,EAASC,GAAwB,OAAOA,GAAM,UAAY,CAAC,MAAMA,CAAC,EAElEC,EAASD,GAAwB,OAAOA,GAAM,SAE9CE,EAAQF,GAA0B,OAAOA,GAAM,WAE/CG,GAAQH,GAAwBC,EAAMD,CAAC,GAAKD,EAAMC,CAAC,EAEnDI,EAAkBJ,GAAYC,EAAMD,CAAC,GAAKE,EAAKF,CAAC,EAAIA,EAAI,KAExDK,GAAoB,CAACC,EAAiCC,IACjED,IAAmB,IAAUP,EAAMO,CAAc,GAAKA,EAAiB,EAAKA,EAAiBC,EAElFC,EAAoBC,GAC/BX,GAAeW,CAAO,GAAKR,EAAMQ,CAAO,GAAKP,EAAKO,CAAO,GAAKV,EAAMU,CAAO,ECjB7E,OAAOC,IAAS,aAAAC,GAAW,mBAAAC,GAAiB,UAAAC,OAAc,QCKnD,SAASC,EAAcC,EAAmBC,EAAkBC,MAAsC,CACvG,GAAM,CAAE,aAAAC,EAAc,MAAAC,CAAM,EAAIJ,EAEhC,sBAAsB,IAAM,CAC1BI,EAAM,UAAY,UAClBA,EAAM,OAASD,EAAe,KAC9BC,EAAM,WAAa,OAAOF,CAAQ,KAElC,sBAAsB,IAAM,CAC1BE,EAAM,OAAS,IACfA,EAAM,QAAU,IAChBA,EAAM,OAAS,IACf,WAAWH,EAAMC,CAAkB,CACrC,CAAC,CACH,CAAC,CACH,CDoCO,SAASG,EAAc,CAC5B,MAAAC,EACA,KAAAC,EACA,eAAAC,EAAiB,GACjB,SAAAC,EAAW,GACX,iBAAAC,KACF,EAAuB,CACrB,OAAO,SAAyB,CAC9B,SAAAC,EACA,SAAAC,EACA,sBAAAC,EACA,KAAAC,EACA,QAAAC,EACA,KAAAC,EACA,UAAAC,CACF,EAAyB,CACvB,IAAMC,EAAiBV,EAAiB,GAAGF,CAAK,KAAKM,CAAQ,GAAKN,EAC5Da,EAAgBX,EAAiB,GAAGD,CAAI,KAAKK,CAAQ,GAAKL,EAC1Da,EAAgBC,GAAO,CAAmB,EAEhD,OAAAC,GAAgB,IAAM,CACpB,IAAMC,EAAOR,EAAQ,QACfS,EAAeN,EAAe,MAAM,GAAG,EAEvCO,EAAaC,GAAsB,CACnCA,EAAE,SAAWX,EAAQ,UAEzBE,EAAU,EACVM,EAAK,oBAAoB,eAAgBE,CAAS,EAClDF,EAAK,oBAAoB,kBAAmBE,CAAS,EACjDL,EAAc,UAAY,GAAuBM,EAAE,OAAS,mBAC9DH,EAAK,UAAU,OAAO,GAAGC,CAAY,EAEzC,GAEgB,IAAM,CACpBD,EAAK,UAAU,IAAI,GAAGC,CAAY,EAClCD,EAAK,iBAAiB,eAAgBE,CAAS,EAC/CF,EAAK,iBAAiB,kBAAmBE,CAAS,CACpD,GAEQ,CACV,EAAG,CAAC,CAAC,EAELE,GAAU,IAAM,CACd,IAAMJ,EAAOR,EAAQ,QAEfa,EAAW,IAAM,CACrBL,EAAK,oBAAoB,eAAgBK,CAAQ,EACjDnB,EAAWoB,EAAcN,EAAMT,EAAMJ,CAAgB,EAAII,EAAK,CAChE,EAQKE,IAAMH,EAAwBe,EAAS,GAN7B,IAAM,CACnBR,EAAc,QAAU,EACxBG,EAAK,WAAa,IAAIJ,CAAa,GACnCI,EAAK,iBAAiB,eAAgBK,CAAQ,CAChD,GAEuD,EACzD,EAAG,CAACZ,CAAI,CAAC,EAEFc,GAAA,cAAAA,GAAA,cAAGnB,CAAS,CACrB,CACF,CEtHA,OAAS,gBAAAoB,GAAc,kBAAAC,OAAoC,QAGpD,SAASC,EAAYC,EAAcC,EAAoC,CAC5E,MAAO,CACL,QAASC,GAAcF,EAAM,QAASA,EAAM,KAAK,EACjD,YAAaA,EAAM,MAAM,YACzB,GAAIA,EAAM,MAAM,QAChB,MAAOA,EAAM,MAAM,MACnB,KAAMA,EAAM,MAAM,KAClB,KAAMA,EAAM,MAAM,MAAQ,CAAC,EAC3B,UAAWA,EAAM,MAAM,UACvB,KAAMA,EAAM,MAAM,KAClB,OAAQA,EAAM,cACd,OAAAC,CACF,CACF,CAEO,SAASC,GAAcC,EAAkBC,EAAmBC,EAAoB,GAAO,CAC5F,OAAIC,GAAeH,CAAO,GAAK,CAACI,EAAMJ,EAAQ,IAAI,EACzCK,GAAgCL,EAA8B,CACnE,WAAYC,EAAM,WAClB,WAAYA,EACZ,KAAMA,EAAM,KACZ,SAAAC,CACF,CAAC,EACQI,EAAKN,CAAO,EACdA,EAAQ,CACb,WAAYC,EAAM,WAClB,WAAYA,EACZ,KAAMA,EAAM,KACZ,SAAAC,CACF,CAAC,EAGIF,CACT,CCrCA,OAAOO,OAAW,QAWX,SAASC,GAAY,CAAE,WAAAC,EAAY,MAAAC,EAAO,UAAAC,EAAY,OAAQ,EAAqB,CACxF,OACEC,GAAA,cAAC,UACC,UAAW,kDAAkFF,CAAK,GAClG,KAAK,SACL,QAASG,GAAK,CACZA,EAAE,gBAAgB,EAClBJ,EAAW,EAAI,CACjB,EACA,aAAYE,GAEZC,GAAA,cAAC,OAAI,cAAY,OAAO,QAAQ,aAC9BA,GAAA,cAAC,QACC,SAAS,UACT,EAAE,2HACJ,CACF,CACF,CAEJ,CC9BA,OAAOE,OAAW,QAClB,OAAOC,OAAQ,OA8DR,SAASC,GAAY,CAC1B,MAAAC,EACA,UAAAC,EACA,WAAAC,EACA,KAAAC,YACA,KAAAC,EACA,UAAAC,EACA,mBAAAC,EACA,SAAAC,EACA,IAAAC,EACA,KAAAC,EACA,MAAAC,CACF,EAAqB,CACnB,IAAMC,EAAWP,GAASE,GAAsBC,IAAa,EACvDK,EAA6B,CACjC,kBAAmB,GAAGZ,CAAK,KAC3B,mBAAoBC,EAAY,UAAY,QAC9C,EAEIK,IAAoBM,EAAM,UAAY,UAAUL,CAAQ,KAC5D,IAAMM,EAAmBC,4BAEvBR,0EAGA,iCAAiDI,CAAK,GACtD,2BAA2CP,CAAI,GAC/C,CACE,8BAA8C,EAAGK,CACnD,CACF,EACMO,EAAaC,EAAKX,CAAS,EAC7BA,EAAU,CACR,IAAAG,EACA,KAAAL,EACA,iBAAAU,CACF,CAAC,EACDC,GAAGD,EAAkBR,CAAS,EAK5BY,EAAiB,CACrB,CAACX,GAAuBC,GAAwB,EAAI,kBAAoB,gBAAgB,EACtFD,GAAuBC,EAAuB,EAC1C,KACA,IAAM,CACJE,GAAQP,EAAW,CACrB,CACR,EAIA,OACEgB,GAAA,cAAC,OAAI,wCAA0D,cAAaP,GAC1EO,GAAA,cAAC,OACC,UAAW,4DAA4FR,CAAK,4BAA4CP,CAAI,GAC9J,EACAe,GAAA,cAAC,OACC,KAAK,cACL,cAAaP,EAAW,OAAS,QACjC,aAAW,qBACX,UAAWI,EACX,MAAOH,EACN,GAAGK,EACN,CACF,CAEJ,CCnIA,OAAOE,OAAQ,OACf,OAAOC,IAAS,aAAAC,GAAW,UAAAC,GAAQ,YAAAC,OAAgB,QCDnD,IAAIC,GAAW,EAEFC,GAAa,IAAM,GAAGD,IAAU,GCatC,SAASE,GACdC,EACAC,EACAC,EACA,CACA,IAAIC,EAAW,EACXC,EAAa,EACbC,EAAiB,CAAC,EAClBC,EAAoB,CAAC,EACrBC,EAAQN,EACNO,EAAS,IAAI,IACbC,EAAY,IAAI,IAEhBC,EAAWC,IACfF,EAAU,IAAIE,CAAM,EACb,IAAMF,EAAU,OAAOE,CAAM,GAGhCA,EAAS,IAAM,CACnBL,EAAW,MAAM,KAAKE,EAAO,OAAO,CAAC,EACrCC,EAAU,QAAQG,GAAMA,EAAG,CAAC,CAC9B,EAEMC,EAAoB,CAAC,CAAE,YAAAC,EAAa,QAAAC,EAAS,SAAAC,CAAS,IAA8B,CACxF,IAAMC,EAAoBH,EAAcA,IAAgBd,EAAKA,IAAO,EAC9DkB,EAAcV,EAAO,IAAIO,CAAO,GAAKC,GAAY,KAEvD,OAAOC,GAAqBC,CAC9B,EAEMC,EAAS,CAACC,EAAYpB,IAAY,CACtCQ,EAAO,QAAQa,GAAK,CA9CxB,IAAAC,GA+CUtB,GAAM,MAAQA,IAAOqB,EAAE,MAAM,YAASC,EAAAD,EAAE,SAAF,MAAAC,EAAA,KAAAD,EAAWD,GACvD,CAAC,CACH,EAEMG,EAAiBH,GAAa,CAnDtC,IAAAE,EAAAE,GAoDIA,GAAAF,EAAAF,EAAE,QAAF,YAAAE,EAAS,UAAT,MAAAE,EAAA,KAAAF,EAAmBF,EAAE,eACrBA,EAAE,SAAW,EACf,EAEMK,EAAezB,GAAY,CAC/B,GAAIA,GAAM,KACRQ,EAAO,QAAQe,CAAa,MACvB,CACL,IAAMF,EAAIb,EAAO,IAAIR,CAAE,EACnBqB,GAAGE,EAAcF,CAAC,CACxB,CACAV,EAAO,CACT,EAEMe,EAAa,IAAM,CACvBtB,GAAcC,EAAM,OACpBA,EAAQ,CAAC,CACX,EAEMsB,EAAkBC,GAAiB,CAvE3C,IAAAN,EAAAE,EAwEI,GAAM,CAAE,QAAAT,EAAS,SAAAC,CAAS,EAAIY,EAAM,MAC9BC,EAAQb,GAAY,KAEtBY,EAAM,SAASpB,EAAO,OAAOoB,EAAM,OAAO,EAC9CA,EAAM,SAAW,GAEjBpB,EAAO,IAAIO,EAASa,CAAK,EACzBjB,EAAO,EACPT,EAAgB4B,EAAYF,EAAOC,EAAQ,QAAU,SAAS,CAAC,EAE3DA,KAAOL,GAAAF,EAAAM,EAAM,OAAM,SAAZ,MAAAJ,EAAA,KAAAF,GACb,EAyEA,MAAO,CACL,GAAAtB,EACA,MAAAO,EACA,QAAAG,EACA,OAAAS,EACA,YAAAM,EACA,OAAAjB,EACA,WAAAkB,EACA,WA/EiB,CAAkBK,EAA8BC,IAAoC,CACrG,GAAInB,EAAkBmB,CAAO,EAAG,OAEhC,GAAM,CAAE,QAAAjB,EAAS,SAAAC,EAAU,KAAAiB,EAAM,QAAAC,EAAS,MAAAC,CAAM,EAAIH,EAE9CI,EAAgBpB,GAAY,KAE9BoB,GAAehC,IAEnB,IAAMiC,EAAa,CACjB,GAAG9B,EACH,MAAOA,EAAM,WACb,IAAKJ,IACL,GAAG,OAAO,YAAY,OAAO,QAAQ6B,CAAO,EAAE,OAAO,CAAC,CAACM,EAAGlB,CAAC,IAAMA,GAAK,IAAI,CAAC,EAC3E,QAAAL,EACA,SAAAC,EACA,KAAAiB,EACA,KAAM,GACN,UAAWM,EAAeP,EAAQ,WAAazB,EAAM,cAAc,EACnE,kBAAmBgC,EAAeP,EAAQ,mBAAqBzB,EAAM,iBAAiB,EACtF,UAAWyB,EAAQ,UAAY,GAAQQ,GAAkBR,EAAQ,UAAWzB,EAAM,SAAS,EAC3F,WAAWkC,EAAe,CACxBjC,EAAO,IAAIO,CAAO,EAAG,cAAgB0B,EACrChB,EAAYV,CAAO,CACrB,EACA,aAAc,CACZ,IAAM2B,EAAgBlC,EAAO,IAAIO,CAAO,EAExC,GAAI2B,GAAiB,KAQrB,IANAxC,EAAgB4B,EAAYY,EAAe,SAAS,CAAC,EACrDlC,EAAO,OAAOO,CAAO,EAErBX,IACIA,EAAa,IAAGA,EAAa,GAE7BC,EAAM,OAAS,EAAG,CACpBsB,EAAetB,EAAM,MAAM,CAAC,EAC5B,MACF,CAEAM,EAAO,EACT,CACF,EAEA0B,EAAW,YAAc9B,EAAM,YAE3ByB,EAAQ,cAAgB,IAASW,EAAcX,EAAQ,WAAW,EACpEK,EAAW,YAAcL,EAAQ,YACxBA,EAAQ,cAAgB,KACjCK,EAAW,YAAcM,EAAcpC,EAAM,WAAW,EAAIA,EAAM,YAAc,IAGlF,IAAMqC,EAAc,CAClB,QAAAb,EACA,MAAOM,EACP,QAAAH,CACF,EAGI3B,EAAM,OAASA,EAAM,MAAQ,GAAKH,EAAaG,EAAM,OAAS6B,EAChE/B,EAAM,KAAKuC,CAAW,EACbC,EAAMV,CAAK,EACpB,WAAW,IAAM,CACfR,EAAeiB,CAAW,CAC5B,EAAGT,CAAK,EAERR,EAAeiB,CAAW,CAE9B,EAWE,SAASE,EAAwB,CAC/BvC,EAAQuC,CACV,EACA,UAAW,CAAC9C,EAAQ+C,IAA6B,CAC/C,IAAM1B,EAAIb,EAAO,IAAIR,CAAE,EACnBqB,IAAGA,EAAE,OAAS0B,EACpB,EACA,cAAgB/C,GAAQ,CA5K5B,IAAAsB,EA4K+B,OAAAA,EAAAd,EAAO,IAAIR,CAAE,IAAb,YAAAsB,EAAgB,UAC3C,YAAa,IAAMhB,CACrB,CACF,CCxJA,IAAM0C,EAAa,IAAI,IACnBC,EAA+B,CAAC,EAC9BC,GAAY,IAAI,IAEhBC,GAAmBC,GAAoBF,GAAU,QAAQG,GAAMA,EAAGD,CAAI,CAAC,EAEvEE,GAAgB,IAAMN,EAAW,KAAO,EAE9C,SAASO,IAAmB,CAC1BN,EAAY,QAAQO,GAAKC,GAAUD,EAAE,QAASA,EAAE,OAAO,CAAC,EACxDP,EAAc,CAAC,CACjB,CAEO,IAAMS,GAAW,CAACC,EAAQ,CAAE,YAAAC,CAAY,IAAiB,CApChE,IAAAC,EAqCE,OAAAA,EAAAb,EAAW,IAAIY,GAAe,CAAoB,IAAlD,YAAAC,EAAqD,OAAO,IAAIF,IAE3D,SAASG,EAAcH,EAAQC,EAAkB,CAvCxD,IAAAC,EAwCE,GAAID,EAAa,MAAO,CAAC,GAACC,EAAAb,EAAW,IAAIY,CAAW,IAA1B,MAAAC,EAA6B,cAAcF,IAErE,IAAII,EAAW,GACf,OAAAf,EAAW,QAAQgB,GAAK,CAClBA,EAAE,cAAcL,CAAE,IAAGI,EAAW,GACtC,CAAC,EAEMA,CACT,CAEO,SAASE,GAAYC,EAA4B,CACtD,GAAI,CAACZ,GAAc,EAAG,CACpBL,EAAcA,EAAY,OAAOO,GAAKU,GAAU,MAAQV,EAAE,QAAQ,UAAYU,CAAM,EACpF,MACF,CAEA,GAAIA,GAAU,MAAQC,GAAKD,CAAM,EAC/BlB,EAAW,QAAQgB,GAAK,CACtBA,EAAE,YAAYE,CAAY,CAC5B,CAAC,UACQA,IAAW,gBAAiBA,GAAU,OAAQA,GAAS,CAChE,IAAME,EAAYpB,EAAW,IAAIkB,EAAO,WAAW,EACnDE,EACIA,EAAU,YAAYF,EAAO,EAAE,EAC/BlB,EAAW,QAAQgB,GAAK,CACtBA,EAAE,YAAYE,EAAO,EAAE,CACzB,CAAC,CACP,CACF,CAEO,IAAMG,GAAoB,CAACC,EAA6B,CAAC,IAAM,CACpEtB,EAAW,QAAQgB,GAAK,CAClBA,EAAE,MAAM,QAAU,CAACM,EAAE,aAAeN,EAAE,KAAOM,EAAE,cACjDN,EAAE,WAAW,CAEjB,CAAC,CACH,EAEO,SAASP,GAAiBc,EAA8BC,EAAiC,CACzFC,EAAcF,CAAO,IACrBjB,GAAc,GAAGL,EAAY,KAAK,CAAE,QAAAsB,EAAS,QAAAC,CAAQ,CAAC,EAE3DxB,EAAW,QAAQgB,GAAK,CACtBA,EAAE,WAAWO,EAASC,CAAO,CAC/B,CAAC,EACH,CAaO,SAASE,GAAeC,EAA0B,CAlGzD,IAAAd,GAmGEA,EAAAb,EAAW,IAAI2B,EAAK,aAAe,CAAoB,IAAvD,MAAAd,EAA0D,UAAUc,EAAK,GAAIA,EAAK,GACpF,CAEO,SAASC,GAAYpB,EAAYqB,EAAyB,CAC/D7B,EAAW,QAAQgB,GAAK,EAClBa,GAAO,MAAQ,EAACA,GAAA,MAAAA,EAAK,eAEdA,GAAA,YAAAA,EAAK,eAAgBb,EAAE,KAChCA,EAAE,OAAOR,EAAGqB,GAAA,YAAAA,EAAK,EAAE,CAEvB,CAAC,CACH,CAEO,SAASC,GAAkBC,EAA4B,CAC5D,IAAMpB,EAAKoB,EAAM,aAAe,EAChC,MAAO,CACL,UAAUC,EAAoB,CAC5B,IAAMZ,EAAYa,GAAwBtB,EAAIoB,EAAO5B,EAAe,EAEpEH,EAAW,IAAIW,EAAIS,CAAS,EAC5B,IAAMc,EAAYd,EAAU,QAAQY,CAAM,EAC1C,OAAAzB,GAAiB,EAEV,IAAM,CACX2B,EAAU,EACVlC,EAAW,OAAOW,CAAE,CACtB,CACF,EACA,SAASW,EAAwB,CA/HrC,IAAAT,GAgIMA,EAAAb,EAAW,IAAIW,CAAE,IAAjB,MAAAE,EAAoB,SAASS,EAC/B,EACA,aAAc,CAlIlB,IAAAT,EAmIM,OAAOA,EAAAb,EAAW,IAAIW,CAAE,IAAjB,YAAAE,EAAoB,aAC7B,CACF,CACF,CAEO,SAASsB,GAAS9B,EAAsB,CAC7C,OAAAH,GAAU,IAAIG,CAAE,EAET,IAAM,CACXH,GAAU,OAAOG,CAAE,CACrB,CACF,CC3HA,SAAS+B,GAAkBC,EAA+B,CACxD,OAAOA,IAAYC,EAAMD,EAAQ,OAAO,GAAKE,EAAMF,EAAQ,OAAO,GAAKA,EAAQ,QAAUG,GAAW,CACtG,CAKA,SAASC,EAAqBC,EAA8BL,EAAqC,CAC/F,OAAAM,GAAUD,EAASL,CAAO,EACnBA,EAAQ,OACjB,CAKA,SAASO,EAAoBC,EAAcR,EAA+B,CACxE,MAAO,CACL,GAAGA,EACH,KAAOA,GAAWA,EAAQ,MAASQ,EACnC,QAAST,GAAWC,CAAO,CAC7B,CACF,CAEA,SAASS,EAAkBD,EAAc,CACvC,MAAO,CAAkBH,EAA8BL,IACrDI,EAAcC,EAASE,EAAaC,EAAMR,CAAO,CAAC,CACtD,CAEA,SAASU,EAAuBL,EAA8BL,EAA+B,CAC3F,OAAOI,EAAcC,EAASE,YAA2BP,CAAO,CAAC,CACnE,CAEAU,EAAM,QAAU,CAAkBL,EAA8BL,IAC9DI,EACEC,EACAE,YAA2B,CACzB,UAAW,GACX,UAAW,GACX,aAAc,GACd,YAAa,GACb,UAAW,GACX,GAAGP,CACL,CAAC,CACH,EAQF,SAASW,GACPC,EACA,CAAE,QAAAC,EAAS,MAAAC,EAAO,QAAAC,CAAQ,EAC1Bf,EACA,CACA,IAAIgB,EAEAH,IACFG,EAAKf,EAAMY,CAAO,EACdH,EAAM,QAAQG,EAASb,CAAO,EAC9BU,EAAM,QAAQG,EAAQ,OAAQ,CAC5B,GAAGb,EACH,GAAIa,CACN,CAA2B,GAGjC,IAAMI,EAAc,CAClB,UAAW,KACX,UAAW,KACX,aAAc,KACd,YAAa,KACb,UAAW,IACb,EAEMC,EAAW,CAAIV,EAAmBW,EAA8CC,IAAc,CAGlG,GAAID,GAAS,KAAM,CACjBT,EAAM,QAAQM,CAAE,EAChB,MACF,CAEA,IAAMK,EAAa,CACjB,KAAAb,EACA,GAAGS,EACH,GAAGjB,EACH,KAAMoB,CACR,EACME,EAASrB,EAAMkB,CAAK,EAAI,CAAE,OAAQA,CAAM,EAAIA,EAGlD,OAAIH,EACFN,EAAM,OAAOM,EAAI,CACf,GAAGK,EACH,GAAGC,CACL,CAAkB,EAGlBZ,EAAMY,EAAQ,OAAQ,CACpB,GAAGD,EACH,GAAGC,CACL,CAAoB,EAGfF,CACT,EAEMG,EAAIC,EAAKZ,CAAO,EAAIA,EAAQ,EAAIA,EAGtC,OAAAW,EAAE,KAAKH,GAAUF,EAAS,UAAWH,EAASK,CAAM,CAAC,EAAE,MAAMK,GAAOP,EAAS,QAASJ,EAAOW,CAAG,CAAC,EAE1FF,CACT,CA2CAb,EAAM,QAAUC,GAChBD,EAAM,QAAUD,WAA8B,EAC9CC,EAAM,KAAOD,QAA2B,EACxCC,EAAM,MAAQD,SAA4B,EAC1CC,EAAM,QAAUD,WAA8B,EAC9CC,EAAM,KAAOA,EAAM,QACnBA,EAAM,KAAO,CAACL,EAAuBL,IACnCI,EACEC,EACAE,YAA2B,CACzB,MAAO,OACP,GAAGP,CACL,CAAC,CACH,EASF,SAAS0B,GAAQJ,EAA4B,CAC3CK,GAAYL,CAAM,CACpB,CAyBAZ,EAAM,QAAUgB,GAKhBhB,EAAM,kBAAoBkB,GAe1BlB,EAAM,SAAWmB,EA+BjBnB,EAAM,OAAS,CAAkBoB,EAAa9B,EAAgC,CAAC,IAAM,CACnF,IAAMU,EAAQqB,GAASD,EAAS9B,CAAuB,EAEvD,GAAIU,EAAO,CACT,GAAM,CAAE,MAAOsB,EAAY,QAASC,CAAW,EAAIvB,EAE7CwB,EAAc,CAClB,MAAO,IACP,GAAGF,EACH,GAAGhC,EACH,QAASA,EAAQ,SAAW8B,EAC5B,SAAU3B,GAAW,CACvB,EAEI+B,EAAY,UAAYJ,IAASI,EAAY,QAAUJ,GAE3D,IAAMzB,EAAU6B,EAAY,QAAUD,EACtC,OAAOC,EAAY,OAEnB9B,EAAcC,EAAS6B,CAAW,CACpC,CACF,EAgBAxB,EAAM,KAAQM,GAAW,CACvBN,EAAM,OAAOM,EAAI,CACf,SAAU,CACZ,CAAC,CACH,EAsBAN,EAAM,SAAWyB,GA2BjBzB,EAAM,KAAQ0B,GAAkBC,GAAY,GAAMD,CAAI,EA2BtD1B,EAAM,MAAS0B,GAAkBC,GAAY,GAAOD,CAAI,ECzYxD,OAAS,UAAAE,GAAQ,wBAAAC,OAA4B,QAItC,SAASC,GAAkBC,EAA4B,CAJ9D,IAAAC,EAKE,GAAM,CAAE,UAAAC,EAAW,YAAAC,EAAa,SAAAC,CAAS,EAAIC,GAAOC,GAAkBN,CAAK,CAAC,EAAE,QAC9EI,EAASJ,CAAK,EACd,IAAMO,GAAWN,EAAAO,GAAqBN,EAAWC,EAAaA,CAAW,IAAxD,YAAAF,EAA2D,QAE5E,SAASQ,EAAoBC,EAAwD,CACnF,GAAI,CAACH,EAAU,MAAO,CAAC,EAEvB,IAAMI,EAAW,IAAI,IAErB,OAAIX,EAAM,aAAaO,EAAS,QAAQ,EAExCA,EAAS,QAAQK,GAAS,CACxB,GAAM,CAAE,SAAAC,CAAS,EAAID,EAAM,MAC3BD,EAAS,IAAIE,CAAQ,GAAKF,EAAS,IAAIE,EAAU,CAAC,CAAC,EACnDF,EAAS,IAAIE,CAAQ,EAAG,KAAKD,CAAK,CACpC,CAAC,EAEM,MAAM,KAAKD,EAAUG,GAAKJ,EAAGI,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,CACjD,CAEA,MAAO,CACL,iBAAAL,EACA,cAAAM,EACA,MAAOR,GAAA,YAAAA,EAAU,MACnB,CACF,CC9BA,OAAwB,aAAAS,GAAW,UAAAC,GAAQ,YAAAC,OAAgB,QAepD,SAASC,GAASC,EAAmB,CAC1C,GAAM,CAACC,EAAWC,CAAY,EAAIC,GAAS,EAAK,EAC1C,CAACC,EAAuBC,CAAwB,EAAIF,GAAS,EAAK,EAClEG,EAAWC,GAAuB,IAAI,EACtCC,EAAOD,GAAkB,CAC7B,MAAO,EACP,MAAO,EACP,gBAAiB,EACjB,gBAAiB,GACjB,QAAS,GACT,QAAS,EACX,CAAC,EAAE,QACG,CAAE,UAAAE,EAAW,aAAAC,EAAc,WAAAC,EAAY,QAAAC,EAAS,aAAAC,CAAa,EAAIb,EAEvEc,GAAe,CACb,GAAId,EAAM,QACV,YAAaA,EAAM,YACnB,GAAIE,CACN,CAAC,EAEDa,GAAU,IAAM,CACd,GAAIf,EAAM,iBACR,OAAAgB,EAAgB,EAET,IAAM,CACXC,EAAkB,CACpB,CAEJ,EAAG,CAACjB,EAAM,gBAAgB,CAAC,EAE3B,SAASgB,GAAkB,CACpB,SAAS,SAAS,GAAGE,EAAW,EAErC,OAAO,iBAAiB,QAASC,CAAS,EAC1C,OAAO,iBAAiB,OAAQD,CAAU,CAC5C,CAEA,SAASD,GAAoB,CAC3B,OAAO,oBAAoB,QAASE,CAAS,EAC7C,OAAO,oBAAoB,OAAQD,CAAU,CAC/C,CAEA,SAASE,EAAYC,EAAoC,CACvD,GAAIrB,EAAM,YAAc,IAAQA,EAAM,YAAcqB,EAAE,YAAa,CACjEC,EAAe,EACf,IAAMC,EAAQjB,EAAS,QACvBE,EAAK,gBAAkB,GACvBA,EAAK,QAAU,GACfe,EAAM,MAAM,WAAa,OAErBvB,EAAM,qBAAuB,KAC/BQ,EAAK,MAAQa,EAAE,QACfb,EAAK,gBAAkBe,EAAM,aAAevB,EAAM,iBAAmB,OAErEQ,EAAK,MAAQa,EAAE,QACfb,EAAK,gBACFe,EAAM,cACJvB,EAAM,mBAAqB,GACxBA,EAAM,iBAAmB,IACzBA,EAAM,kBACZ,IAEN,CACF,CAEA,SAASwB,EAAoBH,EAAoC,CAC/D,GAAM,CAAE,IAAAI,EAAK,OAAAC,EAAQ,KAAAC,EAAM,MAAAC,CAAM,EAAItB,EAAS,QAAS,sBAAsB,EAG3Ee,EAAE,YAAY,OAAS,YACvBrB,EAAM,cACNqB,EAAE,SAAWM,GACbN,EAAE,SAAWO,GACbP,EAAE,SAAWI,GACbJ,EAAE,SAAWK,EAEbR,EAAW,EAEXC,EAAU,CAEd,CAEA,SAASA,GAAY,CACnBjB,EAAa,EAAI,CACnB,CAEA,SAASgB,GAAa,CACpBhB,EAAa,EAAK,CACpB,CAEA,SAASoB,GAAiB,CACxBd,EAAK,QAAU,GACf,SAAS,iBAAiB,cAAeqB,CAAU,EACnD,SAAS,iBAAiB,YAAaC,CAAS,CAClD,CAEA,SAASC,GAAmB,CAC1B,SAAS,oBAAoB,cAAeF,CAAU,EACtD,SAAS,oBAAoB,YAAaC,CAAS,CACrD,CAEA,SAASD,EAAWR,EAAiB,CACnC,IAAME,EAAQjB,EAAS,QACvB,GAAIE,EAAK,SAAWe,EAAO,CACzBf,EAAK,QAAU,GACXP,GAAWiB,EAAW,EACtBlB,EAAM,qBAAuB,IAC/BQ,EAAK,MAAQa,EAAE,QAAUb,EAAK,MAE9BA,EAAK,MAAQa,EAAE,QAAUb,EAAK,MAI5BA,EAAK,QAAUa,EAAE,UAASb,EAAK,gBAAkB,IACrD,IAAMwB,EACJhC,EAAM,qBAAuB,IAAM,GAAGQ,EAAK,KAAK,eAAiB,WAAWA,EAAK,KAAK,iBACxFe,EAAM,MAAM,UAAY,eAAeS,CAAS,MAChDT,EAAM,MAAM,QAAU,GAAG,EAAI,KAAK,IAAIf,EAAK,MAAQA,EAAK,eAAe,CAAC,EAC1E,CACF,CAEA,SAASsB,GAAY,CACnBC,EAAiB,EACjB,IAAMR,EAAQjB,EAAS,QACvB,GAAIE,EAAK,SAAWA,EAAK,SAAWe,EAAO,CAEzC,GADAf,EAAK,QAAU,GACX,KAAK,IAAIA,EAAK,KAAK,EAAIA,EAAK,gBAAiB,CAC/CH,EAAyB,EAAI,EAC7BL,EAAM,WAAW,EAAI,EACrBA,EAAM,YAAY,EAClB,MACF,CAEAuB,EAAM,MAAM,WAAa,+BACzBA,EAAM,MAAM,eAAe,WAAW,EACtCA,EAAM,MAAM,eAAe,SAAS,CACtC,CACF,CAEA,IAAMU,EAA4C,CAChD,cAAeb,EACf,YAAaI,CACf,EAEA,OAAIf,GAAaC,IACfuB,EAAc,aAAef,EAGxBlB,EAAM,UAASiC,EAAc,aAAed,IAI/CN,IACFoB,EAAc,QAAWZ,GAAwB,CAC/CT,GAAWA,EAAQS,CAAC,EACpBb,EAAK,iBAAmBG,EAAW,EAAI,CACzC,GAGK,CACL,UAAAQ,EACA,WAAAD,EACA,UAAAjB,EACA,sBAAAG,EACA,SAAAE,EACA,cAAA2B,CACF,CACF,CCtLA,OAAS,aAAAC,GAAW,mBAAAC,OAAuB,QAEpC,IAAMC,GAA4B,OAAO,QAAW,YAAcD,GAAkBD,GCF3F,OAAOG,OAAQ,OACf,OAAOC,GAAS,gBAAAC,GAAc,kBAAAC,OAAsB,QCDpD,OAAOC,GAAS,gBAAAC,GAAc,kBAAAC,OAAsB,QAgBpD,IAAMC,EAAkC,CAAC,CAAE,MAAAC,EAAO,KAAAC,EAAM,UAAAC,EAAW,GAAGC,CAAK,IACzEC,EAAA,cAAC,OACC,QAAQ,YACR,MAAM,OACN,OAAO,OACP,KAAMJ,IAAU,UAAY,eAAiB,6BAA6BC,CAAI,IAC7E,GAAGE,EACN,EAGF,SAASE,GAAQC,EAAyB,CACxC,OACEF,EAAA,cAACL,EAAA,CAAK,GAAGO,GACPF,EAAA,cAAC,QAAK,EAAE,6eAA6e,CACvf,CAEJ,CAEA,SAASG,GAAKD,EAAyB,CACrC,OACEF,EAAA,cAACL,EAAA,CAAK,GAAGO,GACPF,EAAA,cAAC,QAAK,EAAE,gPAAgP,CAC1P,CAEJ,CAEA,SAASI,GAAQF,EAAyB,CACxC,OACEF,EAAA,cAACL,EAAA,CAAK,GAAGO,GACPF,EAAA,cAAC,QAAK,EAAE,6KAA6K,CACvL,CAEJ,CAEA,SAASK,GAAMH,EAAyB,CACtC,OACEF,EAAA,cAACL,EAAA,CAAK,GAAGO,GACPF,EAAA,cAAC,QAAK,EAAE,qUAAqU,CAC/U,CAEJ,CAEA,SAASM,IAAU,CACjB,OAAON,EAAA,cAAC,OAAI,8BAAgD,CAC9D,CAEO,IAAMO,EAAQ,CACnB,KAAMJ,GACN,QAASF,GACT,QAASG,GACT,MAAOC,GACP,QAASC,EACX,EAEME,GAAaX,GAA6CA,KAAQU,EAIjE,SAASE,GAAQ,CAAE,MAAAb,EAAO,KAAAC,EAAM,UAAAC,EAAW,KAAAY,CAAK,EAAe,CACpE,IAAIC,EAAwB,KACtBC,EAAY,CAAE,MAAAhB,EAAO,KAAAC,CAAK,EAEhC,OAAIa,IAAS,KAEFG,EAAKH,CAAI,EAClBC,EAAOD,EAAK,CAAE,GAAGE,EAAW,UAAAd,CAAU,CAAC,EAC9BgB,GAAeJ,CAAI,EAC5BC,EAAOI,GAAaL,EAAME,CAAS,EAC1Bd,EACTa,EAAOJ,EAAM,QAAQ,EACZC,GAAUX,CAAI,IACvBc,EAAOJ,EAAMV,CAAI,EAAEe,CAAS,IAGvBD,CACT,CDjFO,IAAMK,GAA8BC,GAAS,CAClD,GAAM,CAAE,UAAAC,EAAW,sBAAAC,EAAuB,SAAAC,EAAU,cAAAC,EAAe,UAAAC,CAAU,EAAIC,GAASN,CAAK,EACzF,CACJ,YAAAO,EACA,SAAAC,EACA,UAAAC,EACA,QAAAC,EACA,KAAAC,EACA,gBAAAC,EACA,WAAAC,EACA,WAAYC,EACZ,SAAAC,EACA,UAAAC,EACA,MAAAC,EACA,kBAAAC,EACA,SAAAC,EACA,KAAAC,EACA,SAAAC,EACA,IAAAC,EACA,QAAAC,EACA,YAAAC,EACA,KAAAC,EACA,UAAAC,EACA,aAAAC,EACA,MAAAC,EACA,UAAAC,CACF,EAAI7B,EACE8B,EAAmBC,qBAEvB,0BAA0CH,CAAK,GAC/C,oBAAoCjB,CAAI,GACxC,CACE,uBAAuC,EAAGW,CAC5C,EACA,CACE,kCAAkD,EAAGK,CACvD,CACF,EACMK,EAAaC,EAAKjB,CAAS,EAC7BA,EAAU,CACR,IAAAM,EACA,SAAAP,EACA,KAAAJ,EACA,iBAAAmB,CACF,CAAC,EACDC,GAAGD,EAAkBd,CAAS,EAC5BkB,GAAOC,GAAQnC,CAAK,EACpBoC,GAAuB,CAAC,CAACf,GAAY,CAACZ,EAEtC4B,EAAmB,CAAE,WAAAxB,EAAY,KAAAF,EAAM,MAAAiB,CAAM,EAC/CU,EAAyB,KAE7B,OAAI/B,IAAgB,KAET0B,EAAK1B,CAAW,EACzB+B,EAAQ/B,EAAY8B,CAAgB,EAC3BE,GAAehC,CAAW,EACnC+B,EAAQE,GAAajC,EAAa8B,CAAgB,EAElDC,EAAQG,GAAYJ,CAAgB,GAIpCK,EAAA,cAAC5B,EAAA,CACC,KAAMW,EACN,KAAMD,EACN,SAAUT,EACV,sBAAuBb,EACvB,QAASC,EACT,UAAWE,GAEXqC,EAAA,cAAC,OACC,GAAInB,EACJ,SAAU,EACV,QAASb,EACT,UAASe,EACT,UAAWO,EACV,GAAG5B,EACJ,MAAOa,EACP,IAAKd,EACJ,GAAIsB,GAAQ,CAAE,KAAML,EAAM,aAAcS,CAAU,GAElDK,IAAQ,MACPQ,EAAA,cAAC,OACC,UAAWX,0BAA2C,CACpD,8CAA8E,EAAG,CAACL,CACpF,CAAC,GAEAQ,EACH,EAEDS,GAAcnC,EAAUR,EAAO,CAACC,CAAS,EACzCqC,EACA,CAACtC,EAAM,mBACN0C,EAAA,cAACE,GAAA,CACE,GAAIzB,GAAY,CAACiB,GAAuB,CAAE,IAAK,KAAKjB,CAAQ,EAAG,EAAI,CAAC,EACrE,IAAKG,EACL,MAAOM,EACP,MAAOnB,EACP,UAAWR,EACX,KAAMwB,EACN,WAAYZ,EACZ,KAAMD,EACN,KAAMD,EACN,UAAWO,EACX,mBAAoBkB,GACpB,SAAUf,GAAY,EACxB,CAEJ,CACF,CAEJ,EExHA,IAAMwB,EAAY,CAACC,EAAuBC,EAAiB,MAAW,CACpE,MAAO,+BAA+DD,CAAa,SACnF,KAAM,+BAA+DA,CAAa,QAClF,eAAAC,CACF,GAEMC,GAASC,EAAcJ,EAAU,SAAU,EAAI,CAAC,EAEhDK,GAAQD,EAAcJ,EAAU,QAAS,EAAI,CAAC,EAE9CM,GAAOF,EAAcJ,EAAU,MAAM,CAAC,EAEtCO,GAAOH,EAAcJ,EAAU,MAAM,CAAC,EVHrC,IAAMQ,GAAoC,CAC/C,SAAU,YACV,WAAYC,GACZ,UAAW,IACX,YAAa,GACb,aAAc,GACd,iBAAkB,GAClB,UAAW,QACX,oBACA,uBACA,KAAM,QACN,MAAO,QACP,aAAc,sBACd,QAASC,GAAKA,EAAE,QAAUA,EAAE,OAAS,MACvC,EAEO,SAASC,GAAeC,EAA4B,CACzD,IAAIC,EAAsC,CACxC,GAAGL,GACH,GAAGI,CACL,EACME,EAAUF,EAAM,QAChB,CAACG,EAAWC,CAAc,EAAIC,GAAS,EAAI,EAC3CC,EAAeC,GAAuB,IAAI,EAC1C,CAAE,iBAAAC,EAAkB,cAAAC,EAAe,MAAAC,CAAM,EAAIC,GAAkBV,CAAc,EAC7E,CAAE,UAAAW,EAAW,MAAAC,EAAO,IAAAC,EAAK,YAAAC,EAAa,QAAAC,CAAQ,EAAIf,EAExD,SAASgB,EAAaC,EAAyB,CAC7C,IAAMC,EAAmBC,+BAEvB,8BAA8CF,CAAQ,GACtD,CAAE,iCAAiD,EAAGJ,CAAI,CAC5D,EACA,OAAOO,EAAKT,CAAS,EACjBA,EAAU,CACR,SAAAM,EACA,IAAAJ,EACA,iBAAAK,CACF,CAAC,EACDC,GAAGD,EAAkBG,EAAeV,CAAS,CAAC,CACpD,CAEA,SAASW,GAAc,CACjBrB,IACFE,EAAe,EAAI,EACnBoB,EAAM,KAAK,EAEf,CAEA,OAAAC,GAA0B,IAAM,CA5DlC,IAAAC,EA6DI,GAAIxB,EAAS,CACX,IAAMyB,EAAQrB,EAAa,QAAS,iBAAiB,kBAAkB,EACjEsB,EAAM,GACNC,GAAQH,EAAAzB,EAAe,WAAf,YAAAyB,EAAyB,SAAS,OAC5CI,EAAa,EACbC,EAAQ,EAEZ,MAAM,KAAKJ,CAAK,EACb,QAAQ,EACR,QAAQ,CAACK,EAAGC,IAAM,CACjB,IAAMC,EAAOF,EACbE,EAAK,UAAU,8BAA8C,EAEzDD,EAAI,IAAGC,EAAK,QAAQ,UAAY,GAAG/B,CAAS,IAE3C+B,EAAK,QAAQ,MAAKA,EAAK,QAAQ,IAAML,EAAQ,MAAQ,OAE1D,IAAMM,EAAIL,GAAc3B,EAAY,GAAM,IAAMA,EAAY,EAAIyB,EAAMK,GAEtEC,EAAK,MAAM,YAAY,MAAO,GAAGL,EAAQM,EAAIA,EAAI,EAAE,IAAI,EACvDD,EAAK,MAAM,YAAY,MAAO,GAAGN,CAAG,EAAE,EACtCM,EAAK,MAAM,YAAY,MAAO,GAAG,GAAK/B,EAAY4B,EAAQ,EAAE,EAAE,EAE9DD,GAAcI,EAAK,aACnBH,GAAS,IACX,CAAC,CACL,CACF,EAAG,CAAC5B,EAAWO,EAAOR,CAAO,CAAC,EAE9BkC,GAAU,IAAM,CACd,SAASC,EAAWvC,EAAkB,CA3F1C,IAAA4B,EA4FM,IAAMQ,EAAO5B,EAAa,QACtBU,EAAQlB,CAAC,KACV4B,EAAAQ,EAAK,cAAc,gBAAgB,IAAnC,MAAAR,EAAsD,QACvDtB,EAAe,EAAK,EACpBoB,EAAM,MAAM,GAEV1B,EAAE,MAAQ,WAAa,SAAS,gBAAkBoC,GAAQA,GAAA,MAAAA,EAAM,SAAS,SAAS,kBACpF9B,EAAe,EAAI,EACnBoB,EAAM,KAAK,EAEf,CAEA,gBAAS,iBAAiB,UAAWa,CAAU,EAExC,IAAM,CACX,SAAS,oBAAoB,UAAWA,CAAU,CACpD,CACF,EAAG,CAACrB,CAAO,CAAC,EAGVsB,GAAA,cAAC,WACC,IAAKhC,EACL,qBACA,GAAIS,EACJ,aAAc,IAAM,CACdb,IACFE,EAAe,EAAK,EACpBoB,EAAM,MAAM,EAEhB,EACA,aAAcD,EACd,YAAU,SACV,cAAY,QACZ,gBAAc,iBACd,aAAYtB,EAAe,YAAY,GAEtCO,EAAiB,CAACU,EAAUqB,IAAc,CACzC,IAAMC,EAAuCD,EAAU,OAEnD,CAAE,GAAG1B,CAAM,EADX,CAAE,GAAGA,EAAO,cAAe,MAAO,EAGtC,OACEyB,GAAA,cAAC,OACC,SAAU,GACV,UAAWrB,EAAaC,CAAQ,EAChC,eAAchB,EACd,MAAOsC,EACP,IAAK,KAAKtB,CAAQ,IAEjBqB,EAAU,IAAI,CAAC,CAAE,QAAAE,EAAS,MAAOC,CAAW,IAEzCJ,GAAA,cAACK,GAAA,CACE,GAAGD,EACJ,QAASxC,EACT,YAAaqB,EACb,KAAMd,EAAciC,EAAW,QAASA,EAAW,WAAW,EAC9D,IAAK,KAAKA,EAAW,GAAG,IAEvBD,CACH,CAEH,CACH,CAEJ,CAAC,CACH,CAEJ", "names": ["isValidElement", "isNum", "v", "isStr", "isFn", "isId", "parseClassName", "getAutoCloseDelay", "toastAutoClose", "containerAutoClose", "canBeRendered", "content", "React", "useEffect", "useLayoutEffect", "useRef", "collapseToast", "node", "done", "duration", "scrollHeight", "style", "cssTransition", "enter", "exit", "appendPosition", "collapse", "collapseDuration", "children", "position", "preventExitTransition", "done", "nodeRef", "isIn", "playToast", "enterClassName", "exitClassName", "animationStep", "useRef", "useLayoutEffect", "node", "classToToken", "onEntered", "e", "useEffect", "onExited", "collapseToast", "React", "cloneElement", "isValidElement", "toToastItem", "toast", "status", "renderContent", "content", "props", "isPaused", "isValidElement", "isStr", "cloneElement", "isFn", "React", "CloseButton", "closeToast", "theme", "aria<PERSON><PERSON><PERSON>", "React", "e", "React", "cx", "ProgressBar", "delay", "isRunning", "closeToast", "type", "hide", "className", "controlledProgress", "progress", "rtl", "isIn", "theme", "isHidden", "style", "defaultClassName", "cx", "classNames", "isFn", "animationEvent", "React", "cx", "React", "useEffect", "useRef", "useState", "TOAST_ID", "genToastId", "createContainerObserver", "id", "containerProps", "dispatchChanges", "<PERSON><PERSON><PERSON>", "toastCount", "queue", "snapshot", "props", "toasts", "listeners", "observe", "notify", "cb", "shouldIgnoreToast", "containerId", "toastId", "updateId", "containerMismatch", "isDuplicate", "toggle", "v", "t", "_a", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_b", "removeToast", "clearQueue", "addActiveToast", "toast", "isNew", "toToastItem", "content", "options", "data", "staleId", "delay", "isNotAnUpdate", "toastProps", "_", "parseClassName", "getAutoCloseDelay", "reason", "toast<PERSON>oRemove", "canBeRendered", "activeToast", "isNum", "p", "fn", "containers", "renderQueue", "listeners", "dispatchChanges", "data", "cb", "hasContainers", "flushRenderQueue", "v", "pushToast", "getToast", "id", "containerId", "_a", "isToastActive", "isActive", "c", "removeToast", "params", "isId", "container", "clearWaitingQueue", "p", "content", "options", "canBeRendered", "registerToggle", "opts", "toggleToast", "opt", "registerContainer", "props", "notify", "createContainerObserver", "unobserve", "onChange", "getToastId", "options", "isStr", "isNum", "genToastId", "dispatchToast", "content", "pushToast", "mergeOptions", "type", "createToastByType", "toast", "handlePromise", "promise", "pending", "error", "success", "id", "resetParams", "resolver", "input", "result", "baseParams", "params", "p", "isFn", "err", "dismiss", "removeToast", "clearWaitingQueue", "isToastActive", "toastId", "getToast", "oldOptions", "<PERSON><PERSON><PERSON><PERSON>", "nextOptions", "onChange", "opts", "toggleToast", "useRef", "useSyncExternalStore", "useToastContainer", "props", "_a", "subscribe", "getSnapshot", "setProps", "useRef", "registerContainer", "snapshot", "useSyncExternalStore", "getToastToRender", "cb", "to<PERSON><PERSON>", "toast", "position", "p", "isToastActive", "useEffect", "useRef", "useState", "useToast", "props", "isRunning", "setIsRunning", "useState", "preventExitTransition", "setPreventExitTransition", "toastRef", "useRef", "drag", "autoClose", "pauseOnHover", "closeToast", "onClick", "closeOnClick", "registerToggle", "useEffect", "bindFocusEvents", "unbindFocusEvents", "pauseToast", "playToast", "onDragStart", "e", "bindDragEvents", "toast", "onDragTransitionEnd", "top", "bottom", "left", "right", "onDragMove", "onDragEnd", "unbindDragEvents", "translate", "eventHandlers", "useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "cx", "React", "cloneElement", "isValidElement", "React", "cloneElement", "isValidElement", "Svg", "theme", "type", "isLoading", "rest", "React", "Warning", "props", "Info", "Success", "Error", "Spinner", "Icons", "maybeIcon", "getIcon", "icon", "Icon", "iconProps", "isFn", "isValidElement", "cloneElement", "Toast", "props", "isRunning", "preventExitTransition", "toastRef", "eventHandlers", "playToast", "useToast", "closeButton", "children", "autoClose", "onClick", "type", "hideProgressBar", "closeToast", "Transition", "position", "className", "style", "progressClassName", "updateId", "role", "progress", "rtl", "toastId", "deleteToast", "isIn", "isLoading", "closeOnClick", "theme", "aria<PERSON><PERSON><PERSON>", "defaultClassName", "cx", "cssClasses", "isFn", "icon", "getIcon", "isProgressControlled", "closeButtonProps", "Close", "isValidElement", "cloneElement", "CloseButton", "React", "renderContent", "ProgressBar", "getConfig", "animationName", "appendPosition", "<PERSON><PERSON><PERSON>", "cssTransition", "Slide", "Zoom", "Flip", "defaultProps", "<PERSON><PERSON><PERSON>", "e", "ToastContainer", "props", "containerProps", "stacked", "collapsed", "setIsCollapsed", "useState", "containerRef", "useRef", "getToastToRender", "isToastActive", "count", "useToastContainer", "className", "style", "rtl", "containerId", "hotKeys", "getClassName", "position", "defaultClassName", "cx", "isFn", "parseClassName", "collapseAll", "toast", "useIsomorphicLayoutEffect", "_a", "nodes", "gap", "isTop", "usedHeight", "prevS", "n", "i", "node", "y", "useEffect", "focusFirst", "React", "toastList", "containerStyle", "content", "toastProps", "Toast"]}