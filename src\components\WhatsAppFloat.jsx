import React from 'react';
import { Icon } from '@iconify/react';

export default function WhatsAppFloat() {
  const phoneNumber = '+919006651039';
  const message = 'Hello! I would like to connect with you.';
  
  const handleWhatsAppClick = () => {
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div 
      className="whatsapp-float"
      onClick={handleWhatsAppClick}
      title="Chat with us on WhatsApp"
    >
      <Icon icon="fa-brands:whatsapp" />
    </div>
  );
}
