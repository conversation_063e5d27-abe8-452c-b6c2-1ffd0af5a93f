{"ast": null, "code": "import { store } from '../../store/store';\nimport { sendPost } from '../../api/sendPost';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\nimport { validateParams } from '../../utils/validateParams/validateParams';\nimport { validateTemplateParams } from '../../utils/validateTemplateParams/validateTemplateParams';\nimport { isHeadless } from '../../utils/isHeadless/isHeadless';\nimport { headlessError } from '../../errors/headlessError/headlessError';\nimport { isBlockedValueInParams } from '../../utils/isBlockedValueInParams/isBlockedValueInParams';\nimport { blockedEmailError } from '../../errors/blockedEmailError/blockedEmailError';\nimport { isLimitRateHit } from '../../utils/isLimitRateHit/isLimitRateHit';\nimport { limitRateError } from '../../errors/limitRateError/limitRateError';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templateParams - the template params, what will be set to the EmailJS template\n * @param {object} options - the EmailJS SDK config options\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = async (serviceID, templateID, templateParams, options) => {\n  const opts = buildOptions(options);\n  const publicKey = opts.publicKey || store.publicKey;\n  const blockHeadless = opts.blockHeadless || store.blockHeadless;\n  const storageProvider = opts.storageProvider || store.storageProvider;\n  const blockList = {\n    ...store.blockList,\n    ...opts.blockList\n  };\n  const limitRate = {\n    ...store.limitRate,\n    ...opts.limitRate\n  };\n  if (blockHeadless && isHeadless(navigator)) {\n    return Promise.reject(headlessError());\n  }\n  validateParams(publicKey, serviceID, templateID);\n  validateTemplateParams(templateParams);\n  if (templateParams && isBlockedValueInParams(blockList, templateParams)) {\n    return Promise.reject(blockedEmailError());\n  }\n  if (await isLimitRateHit(location.pathname, limitRate, storageProvider)) {\n    return Promise.reject(limitRateError());\n  }\n  const params = {\n    lib_version: '4.4.1',\n    user_id: publicKey,\n    service_id: serviceID,\n    template_id: templateID,\n    template_params: templateParams\n  };\n  return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n    'Content-type': 'application/json'\n  });\n};", "map": {"version": 3, "names": ["store", "sendPost", "buildOptions", "validateParams", "validateTemplateParams", "isHeadless", "headlessError", "isBlockedValueInParams", "blockedEmailError", "isLimitRateHit", "limitRateError", "send", "serviceID", "templateID", "templateParams", "options", "opts", "public<PERSON>ey", "blockHeadless", "storageProvider", "blockList", "limitRate", "navigator", "Promise", "reject", "location", "pathname", "params", "lib_version", "user_id", "service_id", "template_id", "template_params", "JSON", "stringify"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/methods/send/send.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { sendPost } from '../../api/sendPost';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\nimport { validateParams } from '../../utils/validateParams/validateParams';\nimport { validateTemplateParams } from '../../utils/validateTemplateParams/validateTemplateParams';\nimport { isHeadless } from '../../utils/isHeadless/isHeadless';\nimport { headlessError } from '../../errors/headlessError/headlessError';\nimport { isBlockedValueInParams } from '../../utils/isBlockedValueInParams/isBlockedValueInParams';\nimport { blockedEmailError } from '../../errors/blockedEmailError/blockedEmailError';\nimport { isLimitRateHit } from '../../utils/isLimitRateHit/isLimitRateHit';\nimport { limitRateError } from '../../errors/limitRateError/limitRateError';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templateParams - the template params, what will be set to the EmailJS template\n * @param {object} options - the EmailJS SDK config options\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = async (serviceID, templateID, templateParams, options) => {\n    const opts = buildOptions(options);\n    const publicKey = opts.publicKey || store.publicKey;\n    const blockHeadless = opts.blockHeadless || store.blockHeadless;\n    const storageProvider = opts.storageProvider || store.storageProvider;\n    const blockList = { ...store.blockList, ...opts.blockList };\n    const limitRate = { ...store.limitRate, ...opts.limitRate };\n    if (blockHeadless && isHeadless(navigator)) {\n        return Promise.reject(headlessError());\n    }\n    validateParams(publicKey, serviceID, templateID);\n    validateTemplateParams(templateParams);\n    if (templateParams && isBlockedValueInParams(blockList, templateParams)) {\n        return Promise.reject(blockedEmailError());\n    }\n    if (await isLimitRateHit(location.pathname, limitRate, storageProvider)) {\n        return Promise.reject(limitRateError());\n    }\n    const params = {\n        lib_version: '4.4.1',\n        user_id: publicKey,\n        service_id: serviceID,\n        template_id: templateID,\n        template_params: templateParams,\n    };\n    return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n        'Content-type': 'application/json',\n    });\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,sBAAsB,QAAQ,2DAA2D;AAClG,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,aAAa,QAAQ,0CAA0C;AACxE,SAASC,sBAAsB,QAAQ,2DAA2D;AAClG,SAASC,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,cAAc,QAAQ,4CAA4C;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,EAAEC,cAAc,EAAEC,OAAO,KAAK;EAC1E,MAAMC,IAAI,GAAGd,YAAY,CAACa,OAAO,CAAC;EAClC,MAAME,SAAS,GAAGD,IAAI,CAACC,SAAS,IAAIjB,KAAK,CAACiB,SAAS;EACnD,MAAMC,aAAa,GAAGF,IAAI,CAACE,aAAa,IAAIlB,KAAK,CAACkB,aAAa;EAC/D,MAAMC,eAAe,GAAGH,IAAI,CAACG,eAAe,IAAInB,KAAK,CAACmB,eAAe;EACrE,MAAMC,SAAS,GAAG;IAAE,GAAGpB,KAAK,CAACoB,SAAS;IAAE,GAAGJ,IAAI,CAACI;EAAU,CAAC;EAC3D,MAAMC,SAAS,GAAG;IAAE,GAAGrB,KAAK,CAACqB,SAAS;IAAE,GAAGL,IAAI,CAACK;EAAU,CAAC;EAC3D,IAAIH,aAAa,IAAIb,UAAU,CAACiB,SAAS,CAAC,EAAE;IACxC,OAAOC,OAAO,CAACC,MAAM,CAAClB,aAAa,CAAC,CAAC,CAAC;EAC1C;EACAH,cAAc,CAACc,SAAS,EAAEL,SAAS,EAAEC,UAAU,CAAC;EAChDT,sBAAsB,CAACU,cAAc,CAAC;EACtC,IAAIA,cAAc,IAAIP,sBAAsB,CAACa,SAAS,EAAEN,cAAc,CAAC,EAAE;IACrE,OAAOS,OAAO,CAACC,MAAM,CAAChB,iBAAiB,CAAC,CAAC,CAAC;EAC9C;EACA,IAAI,MAAMC,cAAc,CAACgB,QAAQ,CAACC,QAAQ,EAAEL,SAAS,EAAEF,eAAe,CAAC,EAAE;IACrE,OAAOI,OAAO,CAACC,MAAM,CAACd,cAAc,CAAC,CAAC,CAAC;EAC3C;EACA,MAAMiB,MAAM,GAAG;IACXC,WAAW,EAAE,OAAO;IACpBC,OAAO,EAAEZ,SAAS;IAClBa,UAAU,EAAElB,SAAS;IACrBmB,WAAW,EAAElB,UAAU;IACvBmB,eAAe,EAAElB;EACrB,CAAC;EACD,OAAOb,QAAQ,CAAC,sBAAsB,EAAEgC,IAAI,CAACC,SAAS,CAACP,MAAM,CAAC,EAAE;IAC5D,cAAc,EAAE;EACpB,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}