{"ast": null, "code": "import React from'react';import SocialBtns from'./SocialBtns';import ContactInfo from'./ContactInfo';import ContactForm from'./ContactForm';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function Contact(_ref){let{data,socialData}=_ref;const{sectionHeading,contactImg,contactInfo}=data;return/*#__PURE__*/_jsx(\"section\",{id:\"contactus\",className:\"section contactus-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"contactus-box rounded oveflow-hidden gray-bg\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"row g-0 p-4 p-lg-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-lg-4\"}),/*#__PURE__*/_jsx(\"div\",{className:\"col-lg-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"contactus-title\",children:[/*#__PURE__*/_jsx(\"h5\",{children:sectionHeading.title}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:sectionHeading.subTitle})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"row g-0 contactus-form p-4 p-lg-5 flex-row-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-lg-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"contact-form\",children:/*#__PURE__*/_jsx(ContactForm,{})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-lg-4 pe-md-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"contact-banner d-none d-lg-block\",children:/*#__PURE__*/_jsx(\"img\",{src:contactImg,title:true,alt:\"Avatar\"})}),/*#__PURE__*/_jsx(ContactInfo,{contactInfoData:contactInfo}),/*#__PURE__*/_jsx(SocialBtns,{socialBtns:socialData})]})]})]})})});}", "map": {"version": 3, "names": ["React", "SocialBtns", "ContactInfo", "ContactForm", "jsx", "_jsx", "jsxs", "_jsxs", "Contact", "_ref", "data", "socialData", "sectionHeading", "contactImg", "contactInfo", "id", "className", "children", "title", "subTitle", "src", "alt", "contactInfoData", "socialBtns"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Contact.jsx"], "sourcesContent": ["import React from 'react';\nimport SocialBtns from './SocialBtns';\nimport ContactInfo from './ContactInfo';\nimport ContactForm from './ContactForm';\n\nexport default function Contact({ data, socialData }) {\n  const { sectionHeading, contactImg, contactInfo } = data;\n  return (\n    <section id=\"contactus\" className=\"section contactus-section\">\n      <div className=\"container\">\n        <div className=\"contactus-box rounded oveflow-hidden gray-bg\">\n          <div className=\"row g-0 p-4 p-lg-5\">\n            <div className=\"col-lg-4\" />\n            <div className=\"col-lg-8\">\n              <div\n                className=\"contactus-title\"\n              >\n                <h5>{sectionHeading.title}</h5>\n                <p className=\"m-0\">{sectionHeading.subTitle}</p>\n              </div>\n            </div>\n          </div>\n          <div className=\"row g-0 contactus-form p-4 p-lg-5 flex-row-reverse\">\n            <div className=\"col-lg-8\">\n              <div className=\"contact-form\">\n                <ContactForm />\n              </div>\n            </div>\n            <div className=\"col-lg-4 pe-md-5\">\n              <div className=\"contact-banner d-none d-lg-block\">\n                <img src={contactImg} title alt=\"Avatar\" />\n              </div>\n              <ContactInfo contactInfoData={contactInfo} />\n              <SocialBtns socialBtns={socialData} />\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,MAAO,CAAAC,WAAW,KAAM,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,cAAe,SAAS,CAAAC,OAAOA,CAAAC,IAAA,CAAuB,IAAtB,CAAEC,IAAI,CAAEC,UAAW,CAAC,CAAAF,IAAA,CAClD,KAAM,CAAEG,cAAc,CAAEC,UAAU,CAAEC,WAAY,CAAC,CAAGJ,IAAI,CACxD,mBACEL,IAAA,YAASU,EAAE,CAAC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cAC3DZ,IAAA,QAAKW,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBV,KAAA,QAAKS,SAAS,CAAC,8CAA8C,CAAAC,QAAA,eAC3DV,KAAA,QAAKS,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCZ,IAAA,QAAKW,SAAS,CAAC,UAAU,CAAE,CAAC,cAC5BX,IAAA,QAAKW,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvBV,KAAA,QACES,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAE3BZ,IAAA,OAAAY,QAAA,CAAKL,cAAc,CAACM,KAAK,CAAK,CAAC,cAC/Bb,IAAA,MAAGW,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAEL,cAAc,CAACO,QAAQ,CAAI,CAAC,EAC7C,CAAC,CACH,CAAC,EACH,CAAC,cACNZ,KAAA,QAAKS,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEZ,IAAA,QAAKW,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvBZ,IAAA,QAAKW,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BZ,IAAA,CAACF,WAAW,GAAE,CAAC,CACZ,CAAC,CACH,CAAC,cACNI,KAAA,QAAKS,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BZ,IAAA,QAAKW,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/CZ,IAAA,QAAKe,GAAG,CAAEP,UAAW,CAACK,KAAK,MAACG,GAAG,CAAC,QAAQ,CAAE,CAAC,CACxC,CAAC,cACNhB,IAAA,CAACH,WAAW,EAACoB,eAAe,CAAER,WAAY,CAAE,CAAC,cAC7CT,IAAA,CAACJ,UAAU,EAACsB,UAAU,CAAEZ,UAAW,CAAE,CAAC,EACnC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACC,CAAC,CAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}