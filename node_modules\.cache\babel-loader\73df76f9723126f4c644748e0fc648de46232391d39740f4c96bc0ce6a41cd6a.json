{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Brands.jsx\";\nimport React from 'react';\nimport Slider from 'react-slick';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Brands({\n  data\n}) {\n  var settings = {\n    dots: false,\n    arrows: false,\n    infinite: true,\n    autoplay: true,\n    autoplaySpeed: 0,\n    speed: 3000,\n    slidesToShow: 5,\n    slidesToScroll: 1,\n    initialSlide: 0,\n    cssEase: 'linear',\n    pauseOnHover: true,\n    pauseOnFocus: true,\n    responsive: [{\n      breakpoint: 1400,\n      settings: {\n        slidesToShow: 4\n      }\n    }, {\n      breakpoint: 1200,\n      settings: {\n        slidesToShow: 3\n      }\n    }, {\n      breakpoint: 600,\n      settings: {\n        slidesToShow: 2\n      }\n    }, {\n      breakpoint: 480,\n      settings: {\n        slidesToShow: 2\n      }\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"py-3 py-md-4 brand-section gray-bg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      \"data-aos\": \"fade\",\n      \"data-aos-duration\": \"1200\",\n      \"data-aos-delay\": \"500\",\n      children: /*#__PURE__*/_jsxDEV(Slider, {\n        ...settings,\n        className: \"slider-gap-50\",\n        children: data.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pt-3 pb-3 text-center d-flex align-items-center justify-content-center w-100\",\n            children: item.url ? /*#__PURE__*/_jsxDEV(\"a\", {\n              href: item.url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"d-block w-100\",\n              style: {\n                textDecoration: 'none'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.src,\n                alt: item.alt,\n                className: \"w-100\",\n                style: {\n                  transition: 'transform 0.3s ease',\n                  cursor: 'pointer'\n                },\n                onMouseOver: e => e.target.style.transform = 'scale(1.05)',\n                onMouseOut: e => e.target.style.transform = 'scale(1)'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.src,\n              alt: item.alt,\n              className: \"w-100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_c = Brands;\nvar _c;\n$RefreshReg$(_c, \"Brands\");", "map": {"version": 3, "names": ["React", "Slide<PERSON>", "jsxDEV", "_jsxDEV", "Brands", "data", "settings", "dots", "arrows", "infinite", "autoplay", "autoplaySpeed", "speed", "slidesToShow", "slidesToScroll", "initialSlide", "cssEase", "pauseOnHover", "pauseOnFocus", "responsive", "breakpoint", "className", "children", "map", "item", "index", "url", "href", "target", "rel", "style", "textDecoration", "src", "alt", "transition", "cursor", "onMouseOver", "e", "transform", "onMouseOut", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Brands.jsx"], "sourcesContent": ["import React from 'react';\nimport Slider from 'react-slick';\n\nexport default function Brands({ data }) {\n  var settings = {\n    dots: false,\n    arrows: false,\n    infinite: true,\n    autoplay: true,\n    autoplaySpeed: 0,\n    speed: 3000,\n    slidesToShow: 5,\n    slidesToScroll: 1,\n    initialSlide: 0,\n    cssEase: 'linear',\n    pauseOnHover: true,\n    pauseOnFocus: true,\n    responsive: [\n      {\n        breakpoint: 1400,\n        settings: {\n          slidesToShow: 4,\n        },\n      },\n      {\n        breakpoint: 1200,\n        settings: {\n          slidesToShow: 3,\n        },\n      },\n      {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 2,\n        },\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 2,\n        },\n      },\n    ],\n  };\n  return (\n    <div className=\"py-3 py-md-4 brand-section gray-bg\">\n      <div\n        className=\"container\"\n        data-aos=\"fade\"\n        data-aos-duration=\"1200\"\n        data-aos-delay=\"500\"\n      >\n        <Slider {...settings} className=\"slider-gap-50\">\n          {data.map((item, index) => (\n            <div key={index}>\n              <div className=\"pt-3 pb-3 text-center d-flex align-items-center justify-content-center w-100\">\n                {item.url ? (\n                  <a\n                    href={item.url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"d-block w-100\"\n                    style={{ textDecoration: 'none' }}\n                  >\n                    <img src={item.src} alt={item.alt} className=\"w-100\" style={{ transition: 'transform 0.3s ease', cursor: 'pointer' }}\n                         onMouseOver={(e) => e.target.style.transform = 'scale(1.05)'}\n                         onMouseOut={(e) => e.target.style.transform = 'scale(1)'} />\n                  </a>\n                ) : (\n                  <img src={item.src} alt={item.alt} className=\"w-100\" />\n                )}\n              </div>\n            </div>\n          ))}\n        </Slider>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,eAAe,SAASC,MAAMA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACvC,IAAIC,QAAQ,GAAG;IACbC,IAAI,EAAE,KAAK;IACXC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,CAAC;IAChBC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,OAAO,EAAE,QAAQ;IACjBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,CACV;MACEC,UAAU,EAAE,IAAI;MAChBd,QAAQ,EAAE;QACRO,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEO,UAAU,EAAE,IAAI;MAChBd,QAAQ,EAAE;QACRO,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEO,UAAU,EAAE,GAAG;MACfd,QAAQ,EAAE;QACRO,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEO,UAAU,EAAE,GAAG;MACfd,QAAQ,EAAE;QACRO,YAAY,EAAE;MAChB;IACF,CAAC;EAEL,CAAC;EACD,oBACEV,OAAA;IAAKkB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eACjDnB,OAAA;MACEkB,SAAS,EAAC,WAAW;MACrB,YAAS,MAAM;MACf,qBAAkB,MAAM;MACxB,kBAAe,KAAK;MAAAC,QAAA,eAEpBnB,OAAA,CAACF,MAAM;QAAA,GAAKK,QAAQ;QAAEe,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC5CjB,IAAI,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACpBtB,OAAA;UAAAmB,QAAA,eACEnB,OAAA;YAAKkB,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EAC1FE,IAAI,CAACE,GAAG,gBACPvB,OAAA;cACEwB,IAAI,EAAEH,IAAI,CAACE,GAAI;cACfE,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBR,SAAS,EAAC,eAAe;cACzBS,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO,CAAE;cAAAT,QAAA,eAElCnB,OAAA;gBAAK6B,GAAG,EAAER,IAAI,CAACQ,GAAI;gBAACC,GAAG,EAAET,IAAI,CAACS,GAAI;gBAACZ,SAAS,EAAC,OAAO;gBAACS,KAAK,EAAE;kBAAEI,UAAU,EAAE,qBAAqB;kBAAEC,MAAM,EAAE;gBAAU,CAAE;gBAChHC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACT,MAAM,CAACE,KAAK,CAACQ,SAAS,GAAG,aAAc;gBAC7DC,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACT,MAAM,CAACE,KAAK,CAACQ,SAAS,GAAG;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,gBAEJxC,OAAA;cAAK6B,GAAG,EAAER,IAAI,CAACQ,GAAI;cAACC,GAAG,EAAET,IAAI,CAACS,GAAI;cAACZ,SAAS,EAAC;YAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACvD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAjBElB,KAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GA3EuBxC,MAAM;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}