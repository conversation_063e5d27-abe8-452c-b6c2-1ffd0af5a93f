{"ast": null, "code": "import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const blockedEmailError = () => {\n  return new EmailJSResponseStatus(403, 'Forbidden');\n};", "map": {"version": 3, "names": ["EmailJSResponseStatus", "blockedEmailError"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/errors/blockedEmailError/blockedEmailError.js"], "sourcesContent": ["import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const blockedEmailError = () => {\n    return new EmailJSResponseStatus(403, 'Forbidden');\n};\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,oCAAoC;AAC1E,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EACnC,OAAO,IAAID,qBAAqB,CAAC,GAAG,EAAE,WAAW,CAAC;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}