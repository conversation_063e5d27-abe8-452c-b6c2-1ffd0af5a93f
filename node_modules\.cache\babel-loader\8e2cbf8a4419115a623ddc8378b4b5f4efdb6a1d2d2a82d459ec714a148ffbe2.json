{"ast": null, "code": "import { validateLimitRateParams } from '../validateLimitRateParams/validateLimitRateParams';\nconst getLeftTime = async (id, throttle, storage) => {\n  const lastTime = Number((await storage.get(id)) || 0);\n  return throttle - Date.now() + lastTime;\n};\nexport const isLimitRateHit = async (defaultID, options, storage) => {\n  if (!options.throttle || !storage) {\n    return false;\n  }\n  validateLimitRateParams(options.throttle, options.id);\n  const id = options.id || defaultID;\n  const leftTime = await getLeftTime(id, options.throttle, storage);\n  if (leftTime > 0) {\n    return true;\n  }\n  await storage.set(id, Date.now().toString());\n  return false;\n};", "map": {"version": 3, "names": ["validateLimitRateParams", "getLeftTime", "id", "throttle", "storage", "lastTime", "Number", "get", "Date", "now", "isLimitRateHit", "defaultID", "options", "leftTime", "set", "toString"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/isLimitRateHit/isLimitRateHit.js"], "sourcesContent": ["import { validateLimitRateParams } from '../validateLimitRateParams/validateLimitRateParams';\nconst getLeftTime = async (id, throttle, storage) => {\n    const lastTime = Number((await storage.get(id)) || 0);\n    return throttle - Date.now() + lastTime;\n};\nexport const isLimitRateHit = async (defaultID, options, storage) => {\n    if (!options.throttle || !storage) {\n        return false;\n    }\n    validateLimitRateParams(options.throttle, options.id);\n    const id = options.id || defaultID;\n    const leftTime = await getLeftTime(id, options.throttle, storage);\n    if (leftTime > 0) {\n        return true;\n    }\n    await storage.set(id, Date.now().toString());\n    return false;\n};\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,oDAAoD;AAC5F,MAAMC,WAAW,GAAG,MAAAA,CAAOC,EAAE,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACjD,MAAMC,QAAQ,GAAGC,MAAM,CAAC,CAAC,MAAMF,OAAO,CAACG,GAAG,CAACL,EAAE,CAAC,KAAK,CAAC,CAAC;EACrD,OAAOC,QAAQ,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGJ,QAAQ;AAC3C,CAAC;AACD,OAAO,MAAMK,cAAc,GAAG,MAAAA,CAAOC,SAAS,EAAEC,OAAO,EAAER,OAAO,KAAK;EACjE,IAAI,CAACQ,OAAO,CAACT,QAAQ,IAAI,CAACC,OAAO,EAAE;IAC/B,OAAO,KAAK;EAChB;EACAJ,uBAAuB,CAACY,OAAO,CAACT,QAAQ,EAAES,OAAO,CAACV,EAAE,CAAC;EACrD,MAAMA,EAAE,GAAGU,OAAO,CAACV,EAAE,IAAIS,SAAS;EAClC,MAAME,QAAQ,GAAG,MAAMZ,WAAW,CAACC,EAAE,EAAEU,OAAO,CAACT,QAAQ,EAAEC,OAAO,CAAC;EACjE,IAAIS,QAAQ,GAAG,CAAC,EAAE;IACd,OAAO,IAAI;EACf;EACA,MAAMT,OAAO,CAACU,GAAG,CAACZ,EAAE,EAAEM,IAAI,CAACC,GAAG,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC,CAAC;EAC5C,OAAO,KAAK;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}