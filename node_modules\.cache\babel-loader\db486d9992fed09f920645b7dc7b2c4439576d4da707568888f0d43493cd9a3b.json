{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Contact.jsx\";\nimport React from 'react';\nimport SocialBtns from './SocialBtns';\nimport ContactInfo from './ContactInfo';\nimport ContactForm from './ContactForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Contact({\n  data,\n  socialData\n}) {\n  const {\n    sectionHeading,\n    contactImg,\n    contactInfo\n  } = data;\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"contactus\",\n    className: \"section contactus-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contactus-box rounded oveflow-hidden gray-bg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-0 p-4 p-lg-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contactus-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: sectionHeading.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 18,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"m-0\",\n                children: sectionHeading.subTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-0 contactus-form p-4 p-lg-5 flex-row-reverse\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-form\",\n              children: /*#__PURE__*/_jsxDEV(ContactForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 pe-md-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-banner d-none d-lg-block\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: contactImg,\n                title: true,\n                alt: \"Avatar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ContactInfo, {\n              contactInfoData: contactInfo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SocialBtns, {\n              socialBtns: socialData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "SocialBtns", "ContactInfo", "ContactForm", "jsxDEV", "_jsxDEV", "Contact", "data", "socialData", "sectionHeading", "contactImg", "contactInfo", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "subTitle", "src", "alt", "contactInfoData", "socialBtns", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Contact.jsx"], "sourcesContent": ["import React from 'react';\nimport SocialBtns from './SocialBtns';\nimport ContactInfo from './ContactInfo';\nimport ContactForm from './ContactForm';\n\nexport default function Contact({ data, socialData }) {\n  const { sectionHeading, contactImg, contactInfo } = data;\n  return (\n    <section id=\"contactus\" className=\"section contactus-section\">\n      <div className=\"container\">\n        <div className=\"contactus-box rounded oveflow-hidden gray-bg\">\n          <div className=\"row g-0 p-4 p-lg-5\">\n            <div className=\"col-lg-4\" />\n            <div className=\"col-lg-8\">\n              <div\n                className=\"contactus-title\"\n              >\n                <h5>{sectionHeading.title}</h5>\n                <p className=\"m-0\">{sectionHeading.subTitle}</p>\n              </div>\n            </div>\n          </div>\n          <div className=\"row g-0 contactus-form p-4 p-lg-5 flex-row-reverse\">\n            <div className=\"col-lg-8\">\n              <div className=\"contact-form\">\n                <ContactForm />\n              </div>\n            </div>\n            <div className=\"col-lg-4 pe-md-5\">\n              <div className=\"contact-banner d-none d-lg-block\">\n                <img src={contactImg} title alt=\"Avatar\" />\n              </div>\n              <ContactInfo contactInfoData={contactInfo} />\n              <SocialBtns socialBtns={socialData} />\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,OAAOA,CAAC;EAAEC,IAAI;EAAEC;AAAW,CAAC,EAAE;EACpD,MAAM;IAAEC,cAAc;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGJ,IAAI;EACxD,oBACEF,OAAA;IAASO,EAAE,EAAC,WAAW;IAACC,SAAS,EAAC,2BAA2B;IAAAC,QAAA,eAC3DT,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBT,OAAA;QAAKQ,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DT,OAAA;UAAKQ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCT,OAAA;YAAKQ,SAAS,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5Bb,OAAA;YAAKQ,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBT,OAAA;cACEQ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAE3BT,OAAA;gBAAAS,QAAA,EAAKL,cAAc,CAACU;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/Bb,OAAA;gBAAGQ,SAAS,EAAC,KAAK;gBAAAC,QAAA,EAAEL,cAAc,CAACW;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNb,OAAA;UAAKQ,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBACjET,OAAA;YAAKQ,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBT,OAAA;cAAKQ,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BT,OAAA,CAACF,WAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNb,OAAA;YAAKQ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BT,OAAA;cAAKQ,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAC/CT,OAAA;gBAAKgB,GAAG,EAAEX,UAAW;gBAACS,KAAK;gBAACG,GAAG,EAAC;cAAQ;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNb,OAAA,CAACH,WAAW;cAACqB,eAAe,EAAEZ;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7Cb,OAAA,CAACJ,UAAU;cAACuB,UAAU,EAAEhB;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACO,EAAA,GAnCuBnB,OAAO;AAAA,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}