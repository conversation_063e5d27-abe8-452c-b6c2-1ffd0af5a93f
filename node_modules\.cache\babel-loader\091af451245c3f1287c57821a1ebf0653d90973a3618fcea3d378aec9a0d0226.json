{"ast": null, "code": "import { validateBlockListParams } from '../validateBlockListParams/validateBlockListParams';\nconst isBlockListDisabled = options => {\n  return !options.list?.length || !options.watchVariable;\n};\nconst getValue = (data, name) => {\n  return data instanceof FormData ? data.get(name) : data[name];\n};\nexport const isBlockedValueInParams = (options, params) => {\n  if (isBlockListDisabled(options)) return false;\n  validateBlockListParams(options.list, options.watchVariable);\n  const value = getValue(params, options.watchVariable);\n  if (typeof value !== 'string') return false;\n  return options.list.includes(value);\n};", "map": {"version": 3, "names": ["validateBlockListParams", "isBlockListDisabled", "options", "list", "length", "watchVariable", "getValue", "data", "name", "FormData", "get", "isBlockedValueInParams", "params", "value", "includes"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/isBlockedValueInParams/isBlockedValueInParams.js"], "sourcesContent": ["import { validateBlockListParams } from '../validateBlockListParams/validateBlockListParams';\nconst isBlockListDisabled = (options) => {\n    return !options.list?.length || !options.watchVariable;\n};\nconst getValue = (data, name) => {\n    return data instanceof FormData ? data.get(name) : data[name];\n};\nexport const isBlockedValueInParams = (options, params) => {\n    if (isBlockListDisabled(options))\n        return false;\n    validateBlockListParams(options.list, options.watchVariable);\n    const value = getValue(params, options.watchVariable);\n    if (typeof value !== 'string')\n        return false;\n    return options.list.includes(value);\n};\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,oDAAoD;AAC5F,MAAMC,mBAAmB,GAAIC,OAAO,IAAK;EACrC,OAAO,CAACA,OAAO,CAACC,IAAI,EAAEC,MAAM,IAAI,CAACF,OAAO,CAACG,aAAa;AAC1D,CAAC;AACD,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC7B,OAAOD,IAAI,YAAYE,QAAQ,GAAGF,IAAI,CAACG,GAAG,CAACF,IAAI,CAAC,GAAGD,IAAI,CAACC,IAAI,CAAC;AACjE,CAAC;AACD,OAAO,MAAMG,sBAAsB,GAAGA,CAACT,OAAO,EAAEU,MAAM,KAAK;EACvD,IAAIX,mBAAmB,CAACC,OAAO,CAAC,EAC5B,OAAO,KAAK;EAChBF,uBAAuB,CAACE,OAAO,CAACC,IAAI,EAAED,OAAO,CAACG,aAAa,CAAC;EAC5D,MAAMQ,KAAK,GAAGP,QAAQ,CAACM,MAAM,EAAEV,OAAO,CAACG,aAAa,CAAC;EACrD,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EACzB,OAAO,KAAK;EAChB,OAAOX,OAAO,CAACC,IAAI,CAACW,QAAQ,CAACD,KAAK,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}