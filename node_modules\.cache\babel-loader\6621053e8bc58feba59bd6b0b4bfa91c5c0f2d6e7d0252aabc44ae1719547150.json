{"ast": null, "code": "export class EmailJSResponseStatus {\n  constructor(_status = 0, _text = 'Network Error') {\n    this.status = _status;\n    this.text = _text;\n  }\n}", "map": {"version": 3, "names": ["EmailJSResponseStatus", "constructor", "_status", "_text", "status", "text"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/models/EmailJSResponseStatus.js"], "sourcesContent": ["export class EmailJSResponseStatus {\n    constructor(_status = 0, _text = 'Network Error') {\n        this.status = _status;\n        this.text = _text;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,CAAC;EAC/BC,WAAWA,CAACC,OAAO,GAAG,CAAC,EAAEC,KAAK,GAAG,eAAe,EAAE;IAC9C,IAAI,CAACC,MAAM,GAAGF,OAAO;IACrB,IAAI,CAACG,IAAI,GAAGF,KAAK;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}