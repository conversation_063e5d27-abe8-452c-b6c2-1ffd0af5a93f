{"ast": null, "code": "import{Icon}from'@iconify/react';import React from'react';import SectionHeading from'./SectionHeading';import Ratings from'./Ratings';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function Service(_ref){let{data}=_ref;const{sectionHeading,allService}=data;return/*#__PURE__*/_jsx(\"section\",{className:\"section\",id:\"services\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(SectionHeading,{miniTitle:sectionHeading.miniTitle,title:sectionHeading.title}),/*#__PURE__*/_jsx(\"div\",{className:\"row gy-5\",children:allService===null||allService===void 0?void 0:allService.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"col-sm-6 col-lg-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"services-box\",style:{backgroundImage:`url(${item.imgUrl})`},children:/*#__PURE__*/_jsxs(\"div\",{className:\"services-body\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"icon\",children:/*#__PURE__*/_jsx(Icon,{icon:item.icon})}),/*#__PURE__*/_jsx(\"h5\",{children:item.title}),/*#__PURE__*/_jsx(\"p\",{children:item.subTitle}),/*#__PURE__*/_jsx(\"div\",{className:\"rating-wrap\",children:/*#__PURE__*/_jsx(Ratings,{ratings:item.ratings})})]})})},index))})]})});}", "map": {"version": 3, "names": ["Icon", "React", "SectionHeading", "Ratings", "jsx", "_jsx", "jsxs", "_jsxs", "Service", "_ref", "data", "sectionHeading", "allService", "className", "id", "children", "miniTitle", "title", "map", "item", "index", "style", "backgroundImage", "imgUrl", "icon", "subTitle", "ratings"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Service.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport Ratings from './Ratings';\n\nexport default function Service({ data }) {\n  const { sectionHeading, allService } = data;\n  return (\n    <section className=\"section\" id=\"services\">\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n        />\n        <div className=\"row gy-5\">\n          {allService?.map((item, index) => (\n            <div className=\"col-sm-6 col-lg-3\" key={index}>\n              <div\n                className=\"services-box\"\n                style={{ backgroundImage: `url(${item.imgUrl})` }}\n              >\n                <div className=\"services-body\">\n                  <div className=\"icon\">\n                    <Icon icon={item.icon} />\n                  </div>\n                  <h5>{item.title}</h5>\n                  <p>{item.subTitle}</p>\n                  <div className=\"rating-wrap\">\n                    <Ratings ratings={item.ratings} />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": "AAAA,OAASA,IAAI,KAAQ,gBAAgB,CACrC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,OAAO,KAAM,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhC,cAAe,SAAS,CAAAC,OAAOA,CAAAC,IAAA,CAAW,IAAV,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACtC,KAAM,CAAEE,cAAc,CAAEC,UAAW,CAAC,CAAGF,IAAI,CAC3C,mBACEL,IAAA,YAASQ,SAAS,CAAC,SAAS,CAACC,EAAE,CAAC,UAAU,CAAAC,QAAA,cACxCR,KAAA,QAAKM,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxBV,IAAA,CAACH,cAAc,EACbc,SAAS,CAAEL,cAAc,CAACK,SAAU,CACpCC,KAAK,CAAEN,cAAc,CAACM,KAAM,CAC7B,CAAC,cACFZ,IAAA,QAAKQ,SAAS,CAAC,UAAU,CAAAE,QAAA,CACtBH,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEM,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC3Bf,IAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAE,QAAA,cAChCV,IAAA,QACEQ,SAAS,CAAC,cAAc,CACxBQ,KAAK,CAAE,CAAEC,eAAe,CAAE,OAAOH,IAAI,CAACI,MAAM,GAAI,CAAE,CAAAR,QAAA,cAElDR,KAAA,QAAKM,SAAS,CAAC,eAAe,CAAAE,QAAA,eAC5BV,IAAA,QAAKQ,SAAS,CAAC,MAAM,CAAAE,QAAA,cACnBV,IAAA,CAACL,IAAI,EAACwB,IAAI,CAAEL,IAAI,CAACK,IAAK,CAAE,CAAC,CACtB,CAAC,cACNnB,IAAA,OAAAU,QAAA,CAAKI,IAAI,CAACF,KAAK,CAAK,CAAC,cACrBZ,IAAA,MAAAU,QAAA,CAAII,IAAI,CAACM,QAAQ,CAAI,CAAC,cACtBpB,IAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BV,IAAA,CAACF,OAAO,EAACuB,OAAO,CAAEP,IAAI,CAACO,OAAQ,CAAE,CAAC,CAC/B,CAAC,EACH,CAAC,CACH,CAAC,EAfgCN,KAgBnC,CACN,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}