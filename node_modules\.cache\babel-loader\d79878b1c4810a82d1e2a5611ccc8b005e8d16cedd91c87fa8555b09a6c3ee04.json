{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Experience.jsx\";\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Experience({\n  data\n}) {\n  console.log('Experience data:', data);\n  if (!data) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Experience: No data received\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 12\n    }, this);\n  }\n  const {\n    sectionHeading,\n    allExperience\n  } = data;\n  console.log('Experience allExperience:', allExperience);\n  if (!allExperience) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Experience: No allExperience data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section gray-bg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeading, {\n        miniTitle: sectionHeading === null || sectionHeading === void 0 ? void 0 : sectionHeading.miniTitle,\n        title: sectionHeading === null || sectionHeading === void 0 ? void 0 : sectionHeading.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row gy-3\",\n        children: allExperience === null || allExperience === void 0 ? void 0 : allExperience.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          \"data-aos\": \"fade-up\",\n          \"data-aos-duration\": \"1200\",\n          \"data-aos-delay\": index * 100,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ex-box\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row gy-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 col-lg-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ex-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: item.designation\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 38,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: item.company\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 39,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: item.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: item.jobType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 41,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-8 col-lg-9\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ex-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: item.companyTitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 46,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"m-0\",\n                    children: item.companyDescription\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = Experience;\nvar _c;\n$RefreshReg$(_c, \"Experience\");", "map": {"version": 3, "names": ["React", "SectionHeading", "jsxDEV", "_jsxDEV", "Experience", "data", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sectionHeading", "allExperience", "className", "miniTitle", "title", "map", "item", "index", "designation", "company", "duration", "jobType", "companyTitle", "companyDescription", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Experience.jsx"], "sourcesContent": ["import React from 'react';\nimport SectionHeading from './SectionHeading';\n\nexport default function Experience({ data }) {\n  console.log('Experience data:', data);\n\n  if (!data) {\n    return <div>Experience: No data received</div>;\n  }\n\n  const { sectionHeading, allExperience } = data;\n  console.log('Experience allExperience:', allExperience);\n\n  if (!allExperience) {\n    return <div>Experience: No allExperience data</div>;\n  }\n\n  return (\n    <section className=\"section gray-bg\">\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading?.miniTitle}\n          title={sectionHeading?.title}\n        />\n        <div className=\"row gy-3\">\n          {allExperience?.map((item, index) => (\n            <div\n              className=\"col-12\"\n              key={index}\n              data-aos=\"fade-up\"\n              data-aos-duration=\"1200\"\n              data-aos-delay={index * 100}\n            >\n              <div className=\"ex-box\">\n                <div className=\"row gy-4\">\n                  <div className=\"col-md-4 col-lg-3\">\n                    <div className=\"ex-left\">\n                      <h4>{item.designation}</h4>\n                      <span>{item.company}</span>\n                      <p>{item.duration}</p>\n                      <label>{item.jobType}</label>\n                    </div>\n                  </div>\n                  <div className=\"col-md-8 col-lg-9\">\n                    <div className=\"ex-right\">\n                      <h5>{item.companyTitle}</h5>\n                      <p className=\"m-0\">{item.companyDescription}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,eAAe,SAASC,UAAUA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAC3CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,IAAI,CAAC;EAErC,IAAI,CAACA,IAAI,EAAE;IACT,oBAAOF,OAAA;MAAAK,QAAA,EAAK;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAChD;EAEA,MAAM;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGT,IAAI;EAC9CC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEO,aAAa,CAAC;EAEvD,IAAI,CAACA,aAAa,EAAE;IAClB,oBAAOX,OAAA;MAAAK,QAAA,EAAK;IAAiC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACrD;EAEA,oBACET,OAAA;IAASY,SAAS,EAAC,iBAAiB;IAAAP,QAAA,eAClCL,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAP,QAAA,gBACxBL,OAAA,CAACF,cAAc;QACbe,SAAS,EAAEH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,SAAU;QACrCC,KAAK,EAAEJ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEI;MAAM;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFT,OAAA;QAAKY,SAAS,EAAC,UAAU;QAAAP,QAAA,EACtBM,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9BjB,OAAA;UACEY,SAAS,EAAC,QAAQ;UAElB,YAAS,SAAS;UAClB,qBAAkB,MAAM;UACxB,kBAAgBK,KAAK,GAAG,GAAI;UAAAZ,QAAA,eAE5BL,OAAA;YAAKY,SAAS,EAAC,QAAQ;YAAAP,QAAA,eACrBL,OAAA;cAAKY,SAAS,EAAC,UAAU;cAAAP,QAAA,gBACvBL,OAAA;gBAAKY,SAAS,EAAC,mBAAmB;gBAAAP,QAAA,eAChCL,OAAA;kBAAKY,SAAS,EAAC,SAAS;kBAAAP,QAAA,gBACtBL,OAAA;oBAAAK,QAAA,EAAKW,IAAI,CAACE;kBAAW;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3BT,OAAA;oBAAAK,QAAA,EAAOW,IAAI,CAACG;kBAAO;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BT,OAAA;oBAAAK,QAAA,EAAIW,IAAI,CAACI;kBAAQ;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBT,OAAA;oBAAAK,QAAA,EAAQW,IAAI,CAACK;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKY,SAAS,EAAC,mBAAmB;gBAAAP,QAAA,eAChCL,OAAA;kBAAKY,SAAS,EAAC,UAAU;kBAAAP,QAAA,gBACvBL,OAAA;oBAAAK,QAAA,EAAKW,IAAI,CAACM;kBAAY;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5BT,OAAA;oBAAGY,SAAS,EAAC,KAAK;oBAAAP,QAAA,EAAEW,IAAI,CAACO;kBAAkB;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAtBDQ,KAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACe,EAAA,GAtDuBvB,UAAU;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}