{"ast": null, "code": "import React from'react';import SectionHeading from'./SectionHeading';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function Experience(_ref){let{data}=_ref;const{sectionHeading,allExperience}=data;return/*#__PURE__*/_jsx(\"section\",{className:\"section gray-bg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(SectionHeading,{miniTitle:sectionHeading.miniTitle,title:sectionHeading.title}),/*#__PURE__*/_jsx(\"div\",{className:\"row gy-3\",children:allExperience===null||allExperience===void 0?void 0:allExperience.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",\"data-aos\":\"fade-up\",\"data-aos-duration\":\"1200\",\"data-aos-delay\":index*100,children:/*#__PURE__*/_jsx(\"div\",{className:\"ex-box\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"row gy-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-md-4 col-lg-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"ex-left\",children:[/*#__PURE__*/_jsx(\"h4\",{children:item.designation}),/*#__PURE__*/_jsx(\"span\",{children:item.company}),/*#__PURE__*/_jsx(\"p\",{children:item.duration}),/*#__PURE__*/_jsx(\"label\",{children:item.jobType})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-8 col-lg-9\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"ex-right\",children:[/*#__PURE__*/_jsx(\"h5\",{children:item.companyTitle}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:item.companyDescription})]})})]})})},index))})]})});}", "map": {"version": 3, "names": ["React", "SectionHeading", "jsx", "_jsx", "jsxs", "_jsxs", "Experience", "_ref", "data", "sectionHeading", "allExperience", "className", "children", "miniTitle", "title", "map", "item", "index", "designation", "company", "duration", "jobType", "companyTitle", "companyDescription"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Experience.jsx"], "sourcesContent": ["import React from 'react';\nimport SectionHeading from './SectionHeading';\n\nexport default function Experience({ data }) {\n  const { sectionHeading, allExperience } = data;\n\n  return (\n    <section className=\"section gray-bg\">\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n        />\n        <div className=\"row gy-3\">\n          {allExperience?.map((item, index) => (\n            <div\n              className=\"col-12\"\n              key={index}\n              data-aos=\"fade-up\"\n              data-aos-duration=\"1200\"\n              data-aos-delay={index * 100}\n            >\n              <div className=\"ex-box\">\n                <div className=\"row gy-4\">\n                  <div className=\"col-md-4 col-lg-3\">\n                    <div className=\"ex-left\">\n                      <h4>{item.designation}</h4>\n                      <span>{item.company}</span>\n                      <p>{item.duration}</p>\n                      <label>{item.jobType}</label>\n                    </div>\n                  </div>\n                  <div className=\"col-md-8 col-lg-9\">\n                    <div className=\"ex-right\">\n                      <h5>{item.companyTitle}</h5>\n                      <p className=\"m-0\">{item.companyDescription}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,cAAe,SAAS,CAAAC,UAAUA,CAAAC,IAAA,CAAW,IAAV,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACzC,KAAM,CAAEE,cAAc,CAAEC,aAAc,CAAC,CAAGF,IAAI,CAE9C,mBACEL,IAAA,YAASQ,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAClCP,KAAA,QAAKM,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBT,IAAA,CAACF,cAAc,EACbY,SAAS,CAAEJ,cAAc,CAACI,SAAU,CACpCC,KAAK,CAAEL,cAAc,CAACK,KAAM,CAC7B,CAAC,cACFX,IAAA,QAAKQ,SAAS,CAAC,UAAU,CAAAC,QAAA,CACtBF,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEK,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC9Bd,IAAA,QACEQ,SAAS,CAAC,QAAQ,CAElB,WAAS,SAAS,CAClB,oBAAkB,MAAM,CACxB,iBAAgBM,KAAK,CAAG,GAAI,CAAAL,QAAA,cAE5BT,IAAA,QAAKQ,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrBP,KAAA,QAAKM,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBT,IAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCP,KAAA,QAAKM,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBT,IAAA,OAAAS,QAAA,CAAKI,IAAI,CAACE,WAAW,CAAK,CAAC,cAC3Bf,IAAA,SAAAS,QAAA,CAAOI,IAAI,CAACG,OAAO,CAAO,CAAC,cAC3BhB,IAAA,MAAAS,QAAA,CAAII,IAAI,CAACI,QAAQ,CAAI,CAAC,cACtBjB,IAAA,UAAAS,QAAA,CAAQI,IAAI,CAACK,OAAO,CAAQ,CAAC,EAC1B,CAAC,CACH,CAAC,cACNlB,IAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCP,KAAA,QAAKM,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBT,IAAA,OAAAS,QAAA,CAAKI,IAAI,CAACM,YAAY,CAAK,CAAC,cAC5BnB,IAAA,MAAGQ,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAEI,IAAI,CAACO,kBAAkB,CAAI,CAAC,EAC7C,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,EAtBDN,KAuBF,CACN,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}