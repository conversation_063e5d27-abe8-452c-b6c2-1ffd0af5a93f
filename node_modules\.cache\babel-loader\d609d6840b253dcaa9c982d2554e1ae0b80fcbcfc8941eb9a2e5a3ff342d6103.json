{"ast": null, "code": "export const validateParams = (publicKey, serviceID, templateID) => {\n  if (!publicKey || typeof publicKey !== 'string') {\n    throw 'The public key is required. Visit https://dashboard.emailjs.com/admin/account';\n  }\n  if (!serviceID || typeof serviceID !== 'string') {\n    throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n  }\n  if (!templateID || typeof templateID !== 'string') {\n    throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n  }\n};", "map": {"version": 3, "names": ["validateParams", "public<PERSON>ey", "serviceID", "templateID"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/utils/validateParams/validateParams.js"], "sourcesContent": ["export const validateParams = (publicKey, serviceID, templateID) => {\n    if (!publicKey || typeof publicKey !== 'string') {\n        throw 'The public key is required. Visit https://dashboard.emailjs.com/admin/account';\n    }\n    if (!serviceID || typeof serviceID !== 'string') {\n        throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n    }\n    if (!templateID || typeof templateID !== 'string') {\n        throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,UAAU,KAAK;EAChE,IAAI,CAACF,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IAC7C,MAAM,+EAA+E;EACzF;EACA,IAAI,CAACC,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IAC7C,MAAM,uEAAuE;EACjF;EACA,IAAI,CAACC,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAC/C,MAAM,kFAAkF;EAC5F;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}