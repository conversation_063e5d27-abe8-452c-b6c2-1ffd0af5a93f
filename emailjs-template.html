<div style="font-family: system-ui, sans-serif, <PERSON><PERSON>; font-size: 12px">
  <div>A message by {{name}} has been received. Kindly respond at your earliest convenience.</div>
  <div
    style="
      margin-top: 20px;
      padding: 15px 0;
      border-width: 1px 0;
      border-style: dashed;
      border-color: lightgrey;
    "
  >
    <table role="presentation">
      <tr>
        <td style="vertical-align: top">
          <div
            style="
              padding: 6px 10px;
              margin: 0 10px;
              background-color: aliceblue;
              border-radius: 5px;
              font-size: 26px;
            "
            role="img"
          >
            &#x1F464;
          </div>
        </td>
        <td style="vertical-align: top">
          <div style="color: #2c3e50; font-size: 16px">
            <strong>{{name}}</strong>
          </div>
          <div style="color: #cccccc; font-size: 13px">{{time}}</div>
          <p style="font-size: 16px">{{message}}</p>
          <div style="margin-top: 10px; font-size: 14px; color: #666;">
            <strong>Email:</strong> {{email}}<br>
            <strong>Subject:</strong> {{subject}}
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>

<!-- 
INSTRUCTIONS FOR EMAILJS SETUP:

1. Go to https://emailjs.com and login to your account
2. Go to Email Templates section
3. Click "Create New Template"
4. Set Template ID as: template_portfolio
5. Copy and paste the above HTML code into the template editor
6. Make sure these variables are mapped correctly:
   - {{from_name}} - Sender's name
   - {{from_email}} - Sender's email
   - {{subject}} - Email subject
   - {{message}} - Message content
   - {{to_email}} - Your email (<EMAIL>)
   
7. Test the template and save it
8. Make sure your service ID is: service_l3kiohx
9. Your public key should be: xT5_SFGhfJZhSmwZ2
10. Your private key should be: 1Tvh5NTCV2Cc9Z4L36N-o

The form will now send emails directly to your Gmail account!
-->
