{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import App from'./App';import{BrowserRouter}from'react-router-dom';import'bootstrap/dist/css/bootstrap.min.css';import'slick-carousel/slick/slick.css';import'./scss/style.scss';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsx(App,{})})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport { BrowserRouter } from 'react-router-dom';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'slick-carousel/slick/slick.css';\nimport './scss/style.scss';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <App />\n    </BrowserRouter>\n  </React.StrictMode>,\n);\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,OAASC,aAAa,KAAQ,kBAAkB,CAChD,MAAO,sCAAsC,CAC7C,MAAO,gCAAgC,CACvC,MAAO,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE3B,KAAM,CAAAC,IAAI,CAAGL,QAAQ,CAACM,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACL,KAAK,CAACW,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACF,aAAa,EAAAS,QAAA,cACZP,IAAA,CAACH,GAAG,GAAE,CAAC,CACM,CAAC,CACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}