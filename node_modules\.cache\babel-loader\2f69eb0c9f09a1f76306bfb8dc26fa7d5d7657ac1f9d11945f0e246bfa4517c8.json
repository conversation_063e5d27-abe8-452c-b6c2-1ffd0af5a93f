{"ast": null, "code": "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = async function (url, data) {\n  let headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const response = await fetch(store.origin + url, {\n    method: 'POST',\n    headers,\n    body: data\n  });\n  const message = await response.text();\n  const responseStatus = new EmailJSResponseStatus(response.status, message);\n  if (response.ok) {\n    return responseStatus;\n  }\n  throw responseStatus;\n};", "map": {"version": 3, "names": ["EmailJSResponseStatus", "store", "sendPost", "url", "data", "headers", "arguments", "length", "undefined", "response", "fetch", "origin", "method", "body", "message", "text", "responseStatus", "status", "ok"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/api/sendPost.js"], "sourcesContent": ["import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = async (url, data, headers = {}) => {\n    const response = await fetch(store.origin + url, {\n        method: 'POST',\n        headers,\n        body: data,\n    });\n    const message = await response.text();\n    const responseStatus = new EmailJSResponseStatus(response.status, message);\n    if (response.ok) {\n        return responseStatus;\n    }\n    throw responseStatus;\n};\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,MAAMC,QAAQ,GAAG,eAAAA,CAAOC,GAAG,EAAEC,IAAI,EAAmB;EAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAClD,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACT,KAAK,CAACU,MAAM,GAAGR,GAAG,EAAE;IAC7CS,MAAM,EAAE,MAAM;IACdP,OAAO;IACPQ,IAAI,EAAET;EACV,CAAC,CAAC;EACF,MAAMU,OAAO,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;EACrC,MAAMC,cAAc,GAAG,IAAIhB,qBAAqB,CAACS,QAAQ,CAACQ,MAAM,EAAEH,OAAO,CAAC;EAC1E,IAAIL,QAAQ,CAACS,EAAE,EAAE;IACb,OAAOF,cAAc;EACzB;EACA,MAAMA,cAAc;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}