{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { Route, Routes } from 'react-router-dom';\nimport Home from './pages/Home';\nimport Layout from './components/Layout';\nimport Aos from 'aos';\nimport 'aos/dist/aos.css';\nimport { useEffect } from 'react';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  useEffect(() => {\n    Aos.init({\n      once: true\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 32\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Route, {\n        index: true,\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 31\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["Route", "Routes", "Home", "Layout", "Aos", "useEffect", "ToastContainer", "jsxDEV", "_jsxDEV", "App", "_s", "init", "once", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/App.js"], "sourcesContent": ["import { Route, Routes } from 'react-router-dom';\nimport Home from './pages/Home';\nimport Layout from './components/Layout';\nimport Aos from 'aos';\nimport 'aos/dist/aos.css';\nimport { useEffect } from 'react';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nfunction App() {\n  useEffect(() => {\n    Aos.init({\n      once: true,\n    });\n  }, []);\n  return (\n    <Routes>\n      <Route path=\"/\" element={<Layout />}>\n        <Route index element={<Home />} />\n      </Route>\n    </Routes>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AAChD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AACzB,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACbL,SAAS,CAAC,MAAM;IACdD,GAAG,CAACO,IAAI,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,oBACEJ,OAAA,CAACP,MAAM;IAAAY,QAAA,eACLL,OAAA,CAACR,KAAK;MAACc,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEP,OAAA,CAACL,MAAM;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAN,QAAA,eAClCL,OAAA,CAACR,KAAK;QAACoB,KAAK;QAACL,OAAO,eAAEP,OAAA,CAACN,IAAI;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEb;AAACT,EAAA,CAbQD,GAAG;AAAAY,EAAA,GAAHZ,GAAG;AAeZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}