{"ast": null, "code": "import React,{useState,useEffect}from'react';import emailjs from'@emailjs/browser';import{toast}from'react-toastify';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function ContactForm(){// Initialize EmailJS\nuseEffect(()=>{emailjs.init('xT5_SFGhfJZhSmwZ2');// Your public key\n},[]);const[loading,setLoading]=useState(false);const[formData,setFormData]=useState({name:'',email:'',subject:'',message:''});// Handler for input field changes\nconst handleInputChange=event=>{const{name,value}=event.target;setFormData(prevFormData=>({...prevFormData,[name]:value}));};const onSubmit=async event=>{event.preventDefault();setLoading(true);console.log('🚀 EmailJS Form Submission Started');console.log('Form Data:',formData);try{// EmailJS configuration\nconst serviceID='service_l3kiohx';const templateID='template_7dfyeuo';// Your actual template ID from EmailJS\n// Template parameters for EmailJS - matching your template variables\nconst templateParams={name:formData.name,// {{name}} in your template\nmessage:formData.message,// {{message}} in your template\ntime:new Date().toLocaleString('en-IN',{timeZone:'Asia/Kolkata',year:'numeric',month:'short',day:'numeric',hour:'2-digit',minute:'2-digit'}),// {{time}} in your template\nemail:formData.email,// {{email}} in your template\nsubject:formData.subject// {{subject}} in your template\n};console.log('Sending email with params:',templateParams);const result=await emailjs.send(serviceID,templateID,templateParams);console.log('EmailJS Result:',result);if(result.status===200){// Clear form data\nsetFormData({name:'',email:'',subject:'',message:''});// Show success toast\ntoast.success('🎉 Message sent successfully! I\\'ll get back to you soon.',{position:\"top-right\",autoClose:5000,hideProgressBar:false,closeOnClick:true,pauseOnHover:true,draggable:true});console.log('Email sent successfully!');}else{throw new Error('Email sending failed');}}catch(error){console.error('EmailJS Error:',error);// Show error toast\ntoast.error('❌ Failed to send message. Please try again or contact me directly.',{position:\"top-right\",autoClose:5000,hideProgressBar:false,closeOnClick:true,pauseOnHover:true,draggable:true});// More detailed error logging\nif(error.text){console.error('Error details:',error.text);}}finally{setLoading(false);}};return/*#__PURE__*/_jsx(\"form\",{id:\"contact-form\",onSubmit:onSubmit,children:/*#__PURE__*/_jsxs(\"div\",{className:\"row gx-3 gy-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"First Name\"}),/*#__PURE__*/_jsx(\"input\",{name:\"name\",placeholder:\"Name *\",className:\"form-control\",type:\"text\",value:formData.name,onChange:handleInputChange,required:true})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"input\",{name:\"lastName\",placeholder:\"Last Name\",className:\"form-control\",type:\"text\",value:formData.lastName,onChange:handleInputChange})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"Phone Number\"}),/*#__PURE__*/_jsx(\"input\",{name:\"phone\",placeholder:\"Phone Number *\",className:\"form-control\",type:\"tel\",value:formData.phone,onChange:handleInputChange,required:true,pattern:\"[0-9]{10}\"// optional validation for 10-digit numbers\n})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{name:\"email\",placeholder:\"l\",className:\"form-control\",type:\"email\",value:formData.email,onChange:handleInputChange})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"Subject\"}),/*#__PURE__*/_jsx(\"input\",{name:\"subject\",placeholder:\"Subject *\",className:\"form-control\",type:\"text\",value:formData.subject,onChange:handleInputChange,required:true})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"message\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"message\",placeholder:\"Your message\",rows:4,className:\"form-control\",value:formData.message,onChange:handleInputChange})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-md-12\",children:/*#__PURE__*/_jsx(\"div\",{className:\"send\",children:/*#__PURE__*/_jsx(\"button\",{className:`px-btn w-100 ${loading?'disabled':''}`,type:\"submit\",disabled:loading,children:loading?'Sending...':'Send Message'})})})]})});}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "emailjs", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "ContactForm", "init", "loading", "setLoading", "formData", "setFormData", "name", "email", "subject", "message", "handleInputChange", "event", "value", "target", "prevFormData", "onSubmit", "preventDefault", "console", "log", "serviceID", "templateID", "templateParams", "time", "Date", "toLocaleString", "timeZone", "year", "month", "day", "hour", "minute", "result", "send", "status", "success", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "Error", "error", "text", "id", "children", "className", "placeholder", "type", "onChange", "required", "lastName", "phone", "pattern", "rows", "disabled"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/ContactForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport emailjs from '@emailjs/browser';\nimport { toast } from 'react-toastify';\n\nexport default function ContactForm() {\n  // Initialize EmailJS\n  useEffect(() => {\n    emailjs.init('xT5_SFGhfJZhSmwZ2'); // Your public key\n  }, []);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n  });\n\n  // Handler for input field changes\n  const handleInputChange = event => {\n    const { name, value } = event.target;\n    setFormData(prevFormData => ({\n      ...prevFormData,\n      [name]: value,\n    }));\n  };\n\n  const onSubmit = async event => {\n    event.preventDefault();\n    setLoading(true);\n\n    console.log('🚀 EmailJS Form Submission Started');\n    console.log('Form Data:', formData);\n\n    try {\n      // EmailJS configuration\n      const serviceID = 'service_l3kiohx';\n      const templateID = 'template_7dfyeuo'; // Your actual template ID from EmailJS\n\n      // Template parameters for EmailJS - matching your template variables\n      const templateParams = {\n        name: formData.name,           // {{name}} in your template\n        message: formData.message,     // {{message}} in your template\n        time: new Date().toLocaleString('en-IN', {\n          timeZone: 'Asia/Kolkata',\n          year: 'numeric',\n          month: 'short',\n          day: 'numeric',\n          hour: '2-digit',\n          minute: '2-digit'\n        }), // {{time}} in your template\n        email: formData.email,         // {{email}} in your template\n        subject: formData.subject,     // {{subject}} in your template\n      };\n\n      console.log('Sending email with params:', templateParams);\n\n      const result = await emailjs.send(serviceID, templateID, templateParams);\n\n      console.log('EmailJS Result:', result);\n\n      if (result.status === 200) {\n        // Clear form data\n        setFormData({ name: '', email: '', subject: '', message: '' });\n\n        // Show success toast\n        toast.success('🎉 Message sent successfully! I\\'ll get back to you soon.', {\n          position: \"top-right\",\n          autoClose: 5000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true,\n        });\n\n        console.log('Email sent successfully!');\n      } else {\n        throw new Error('Email sending failed');\n      }\n    } catch (error) {\n      console.error('EmailJS Error:', error);\n\n      // Show error toast\n      toast.error('❌ Failed to send message. Please try again or contact me directly.', {\n        position: \"top-right\",\n        autoClose: 5000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true,\n      });\n\n      // More detailed error logging\n      if (error.text) {\n        console.error('Error details:', error.text);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return (\n    <form id=\"contact-form\" onSubmit={onSubmit}>\n      <div className=\"row gx-3 gy-4\">\n        <div className=\"col-md-6\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">First Name</label>\n            <input\n              name=\"name\"\n              placeholder=\"Name *\"\n              className=\"form-control\"\n              type=\"text\"\n              value={formData.name}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-md-6\">\n  <div className=\"form-group\">\n    <label className=\"form-label\">Last Name</label>\n    <input\n      name=\"lastName\"\n      placeholder=\"Last Name\"\n      className=\"form-control\"\n      type=\"text\"\n      value={formData.lastName}\n      onChange={handleInputChange}     \n    />\n  </div>\n</div>\n        <div className=\"col-md-6\">\n  <div className=\"form-group\">\n    <label className=\"form-label\">Phone Number</label>\n    <input\n      name=\"phone\"\n      placeholder=\"Phone Number *\"\n      className=\"form-control\"\n      type=\"tel\"\n      value={formData.phone}\n      onChange={handleInputChange}\n      required\n      pattern=\"[0-9]{10}\" // optional validation for 10-digit numbers\n    />\n  </div>\n</div>\n        <div className=\"col-md-6\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Email</label>\n            <input\n              name=\"email\"\n              placeholder=\"l\"\n              className=\"form-control\"\n              type=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n            \n            />\n          </div>\n        </div>\n        <div className=\"col-12\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">Subject</label>\n            <input\n              name=\"subject\"\n              placeholder=\"Subject *\"\n              className=\"form-control\"\n              type=\"text\"\n              value={formData.subject}\n              onChange={handleInputChange}\n              required\n            />\n          </div>\n        </div>\n        <div className=\"col-md-12\">\n          <div className=\"form-group\">\n            <label className=\"form-label\">message</label>\n            <textarea\n              name=\"message\"\n              placeholder=\"Your message\"\n              rows={4}\n              className=\"form-control\"\n              value={formData.message}\n              onChange={handleInputChange}\n            />\n          </div>\n        </div>\n        <div className=\"col-md-12\">\n          <div className=\"send\">\n            <button\n              className={`px-btn w-100 ${loading ? 'disabled' : ''}`}\n              type=\"submit\"\n              disabled={loading}\n            >\n              {loading ? 'Sending...' : 'Send Message'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </form>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,OAAO,KAAM,kBAAkB,CACtC,OAASC,KAAK,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,cAAe,SAAS,CAAAC,WAAWA,CAAA,CAAG,CACpC;AACAP,SAAS,CAAC,IAAM,CACdC,OAAO,CAACO,IAAI,CAAC,mBAAmB,CAAC,CAAE;AACrC,CAAC,CAAE,EAAE,CAAC,CACN,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGX,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACY,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAC,CACvCc,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,EACX,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,iBAAiB,CAAGC,KAAK,EAAI,CACjC,KAAM,CAAEL,IAAI,CAAEM,KAAM,CAAC,CAAGD,KAAK,CAACE,MAAM,CACpCR,WAAW,CAACS,YAAY,GAAK,CAC3B,GAAGA,YAAY,CACf,CAACR,IAAI,EAAGM,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAJ,KAAK,EAAI,CAC9BA,KAAK,CAACK,cAAc,CAAC,CAAC,CACtBb,UAAU,CAAC,IAAI,CAAC,CAEhBc,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjDD,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEd,QAAQ,CAAC,CAEnC,GAAI,CACF;AACA,KAAM,CAAAe,SAAS,CAAG,iBAAiB,CACnC,KAAM,CAAAC,UAAU,CAAG,kBAAkB,CAAE;AAEvC;AACA,KAAM,CAAAC,cAAc,CAAG,CACrBf,IAAI,CAAEF,QAAQ,CAACE,IAAI,CAAY;AAC/BG,OAAO,CAAEL,QAAQ,CAACK,OAAO,CAAM;AAC/Ba,IAAI,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAE,CACvCC,QAAQ,CAAE,cAAc,CACxBC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CAAE;AACJvB,KAAK,CAAEH,QAAQ,CAACG,KAAK,CAAU;AAC/BC,OAAO,CAAEJ,QAAQ,CAACI,OAAa;AACjC,CAAC,CAEDS,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEG,cAAc,CAAC,CAEzD,KAAM,CAAAU,MAAM,CAAG,KAAM,CAAArC,OAAO,CAACsC,IAAI,CAACb,SAAS,CAAEC,UAAU,CAAEC,cAAc,CAAC,CAExEJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEa,MAAM,CAAC,CAEtC,GAAIA,MAAM,CAACE,MAAM,GAAK,GAAG,CAAE,CACzB;AACA5B,WAAW,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAE9D;AACAd,KAAK,CAACuC,OAAO,CAAC,2DAA2D,CAAE,CACzEC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,IAAI,CACfC,eAAe,CAAE,KAAK,CACtBC,YAAY,CAAE,IAAI,CAClBC,YAAY,CAAE,IAAI,CAClBC,SAAS,CAAE,IACb,CAAC,CAAC,CAEFvB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACzC,CAAC,IAAM,CACL,KAAM,IAAI,CAAAuB,KAAK,CAAC,sBAAsB,CAAC,CACzC,CACF,CAAE,MAAOC,KAAK,CAAE,CACdzB,OAAO,CAACyB,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CAEtC;AACA/C,KAAK,CAAC+C,KAAK,CAAC,oEAAoE,CAAE,CAChFP,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,IAAI,CACfC,eAAe,CAAE,KAAK,CACtBC,YAAY,CAAE,IAAI,CAClBC,YAAY,CAAE,IAAI,CAClBC,SAAS,CAAE,IACb,CAAC,CAAC,CAEF;AACA,GAAIE,KAAK,CAACC,IAAI,CAAE,CACd1B,OAAO,CAACyB,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAACC,IAAI,CAAC,CAC7C,CACF,CAAC,OAAS,CACRxC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CACD,mBACEN,IAAA,SAAM+C,EAAE,CAAC,cAAc,CAAC7B,QAAQ,CAAEA,QAAS,CAAA8B,QAAA,cACzC9C,KAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BhD,IAAA,QAAKiD,SAAS,CAAC,UAAU,CAAAD,QAAA,cACvB9C,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhD,IAAA,UAAOiD,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,YAAU,CAAO,CAAC,cAChDhD,IAAA,UACES,IAAI,CAAC,MAAM,CACXyC,WAAW,CAAC,QAAQ,CACpBD,SAAS,CAAC,cAAc,CACxBE,IAAI,CAAC,MAAM,CACXpC,KAAK,CAAER,QAAQ,CAACE,IAAK,CACrB2C,QAAQ,CAAEvC,iBAAkB,CAC5BwC,QAAQ,MACT,CAAC,EACC,CAAC,CACH,CAAC,cACNrD,IAAA,QAAKiD,SAAS,CAAC,UAAU,CAAAD,QAAA,cAC/B9C,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhD,IAAA,UAAOiD,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,WAAS,CAAO,CAAC,cAC/ChD,IAAA,UACES,IAAI,CAAC,UAAU,CACfyC,WAAW,CAAC,WAAW,CACvBD,SAAS,CAAC,cAAc,CACxBE,IAAI,CAAC,MAAM,CACXpC,KAAK,CAAER,QAAQ,CAAC+C,QAAS,CACzBF,QAAQ,CAAEvC,iBAAkB,CAC7B,CAAC,EACC,CAAC,CACH,CAAC,cACEb,IAAA,QAAKiD,SAAS,CAAC,UAAU,CAAAD,QAAA,cAC/B9C,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhD,IAAA,UAAOiD,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,cAAY,CAAO,CAAC,cAClDhD,IAAA,UACES,IAAI,CAAC,OAAO,CACZyC,WAAW,CAAC,gBAAgB,CAC5BD,SAAS,CAAC,cAAc,CACxBE,IAAI,CAAC,KAAK,CACVpC,KAAK,CAAER,QAAQ,CAACgD,KAAM,CACtBH,QAAQ,CAAEvC,iBAAkB,CAC5BwC,QAAQ,MACRG,OAAO,CAAC,WAAY;AAAA,CACrB,CAAC,EACC,CAAC,CACH,CAAC,cACExD,IAAA,QAAKiD,SAAS,CAAC,UAAU,CAAAD,QAAA,cACvB9C,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhD,IAAA,UAAOiD,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,OAAK,CAAO,CAAC,cAC3ChD,IAAA,UACES,IAAI,CAAC,OAAO,CACZyC,WAAW,CAAC,GAAG,CACfD,SAAS,CAAC,cAAc,CACxBE,IAAI,CAAC,OAAO,CACZpC,KAAK,CAAER,QAAQ,CAACG,KAAM,CACtB0C,QAAQ,CAAEvC,iBAAkB,CAE7B,CAAC,EACC,CAAC,CACH,CAAC,cACNb,IAAA,QAAKiD,SAAS,CAAC,QAAQ,CAAAD,QAAA,cACrB9C,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhD,IAAA,UAAOiD,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,SAAO,CAAO,CAAC,cAC7ChD,IAAA,UACES,IAAI,CAAC,SAAS,CACdyC,WAAW,CAAC,WAAW,CACvBD,SAAS,CAAC,cAAc,CACxBE,IAAI,CAAC,MAAM,CACXpC,KAAK,CAAER,QAAQ,CAACI,OAAQ,CACxByC,QAAQ,CAAEvC,iBAAkB,CAC5BwC,QAAQ,MACT,CAAC,EACC,CAAC,CACH,CAAC,cACNrD,IAAA,QAAKiD,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxB9C,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhD,IAAA,UAAOiD,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,SAAO,CAAO,CAAC,cAC7ChD,IAAA,aACES,IAAI,CAAC,SAAS,CACdyC,WAAW,CAAC,cAAc,CAC1BO,IAAI,CAAE,CAAE,CACRR,SAAS,CAAC,cAAc,CACxBlC,KAAK,CAAER,QAAQ,CAACK,OAAQ,CACxBwC,QAAQ,CAAEvC,iBAAkB,CAC7B,CAAC,EACC,CAAC,CACH,CAAC,cACNb,IAAA,QAAKiD,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBhD,IAAA,QAAKiD,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBhD,IAAA,WACEiD,SAAS,CAAE,gBAAgB5C,OAAO,CAAG,UAAU,CAAG,EAAE,EAAG,CACvD8C,IAAI,CAAC,QAAQ,CACbO,QAAQ,CAAErD,OAAQ,CAAA2C,QAAA,CAEjB3C,OAAO,CAAG,YAAY,CAAG,cAAc,CAClC,CAAC,CACN,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,CAEX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}