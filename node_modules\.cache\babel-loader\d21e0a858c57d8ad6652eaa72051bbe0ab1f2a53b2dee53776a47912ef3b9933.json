{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { Route, Routes } from 'react-router-dom';\nimport Home from './pages/Home';\nimport Layout from './components/Layout';\nimport Aos from 'aos';\nimport 'aos/dist/aos.css';\nimport { useEffect } from 'react';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  useEffect(() => {\n    Aos.init({\n      once: true,\n      duration: 1200,\n      offset: 100,\n      delay: 0\n    });\n\n    // Refresh AOS on window resize\n    const handleResize = () => {\n      Aos.refresh();\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Routes, {\n      children: /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 34\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Route, {\n          index: true,\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["Route", "Routes", "Home", "Layout", "Aos", "useEffect", "ToastContainer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "init", "once", "duration", "offset", "delay", "handleResize", "refresh", "window", "addEventListener", "removeEventListener", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/App.js"], "sourcesContent": ["import { Route, Routes } from 'react-router-dom';\nimport Home from './pages/Home';\nimport Layout from './components/Layout';\nimport Aos from 'aos';\nimport 'aos/dist/aos.css';\nimport { useEffect } from 'react';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nfunction App() {\n  useEffect(() => {\n    Aos.init({\n      once: true,\n      duration: 1200,\n      offset: 100,\n      delay: 0,\n    });\n\n    // Refresh AOS on window resize\n    const handleResize = () => {\n      Aos.refresh();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n  return (\n    <>\n      <Routes>\n        <Route path=\"/\" element={<Layout />}>\n          <Route index element={<Home />} />\n        </Route>\n      </Routes>\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={5000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n    </>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AAChD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AACzB,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACbP,SAAS,CAAC,MAAM;IACdD,GAAG,CAACS,IAAI,CAAC;MACPC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,GAAG;MACXC,KAAK,EAAE;IACT,CAAC,CAAC;;IAEF;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBd,GAAG,CAACe,OAAO,CAAC,CAAC;IACf,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,oBACEV,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACEf,OAAA,CAACP,MAAM;MAAAsB,QAAA,eACLf,OAAA,CAACR,KAAK;QAACwB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACL,MAAM;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,eAClCf,OAAA,CAACR,KAAK;UAAC8B,KAAK;UAACL,OAAO,eAAEjB,OAAA,CAACN,IAAI;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACTrB,OAAA,CAACF,cAAc;MACbyB,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA,eACF,CAAC;AAEP;AAACjB,EAAA,CAzCQD,GAAG;AAAA8B,EAAA,GAAH9B,GAAG;AA2CZ,eAAeA,GAAG;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}