{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\About.jsx\";\nimport { Icon } from '@iconify/react';\nimport React from 'react';\nimport parser from 'html-react-parser';\nimport { Link as ScrollLink } from 'react-scroll';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function About({\n  data\n}) {\n  const {\n    imgSrc,\n    miniTitle,\n    title,\n    description,\n    funfacts,\n    btnText,\n    btnUrl\n  } = data;\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"about-section section\",\n    id: \"about\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"effect-1\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/effect-1.svg\",\n          alt: \"Shape\",\n          \"data-aos\": \"zoom-in\",\n          \"data-aos-duration\": \"1200\",\n          \"data-aos-delay\": \"500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"effect-2\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/effect-2.svg\",\n          alt: \"Shape\",\n          \"data-aos\": \"zoom-in\",\n          \"data-aos-duration\": \"1200\",\n          \"data-aos-delay\": \"400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row align-items-center justify-content-center gy-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-lg-6 col-xl-5\",\n          \"data-aos\": \"fade-right\",\n          \"data-aos-duration\": \"1200\",\n          \"data-aos-delay\": \"500\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"about-banner text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: imgSrc,\n              alt: \"Thumb\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-lg-6 col-xl-5 px-lg-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"about-text\",\n            \"data-aos\": \"fade\",\n            \"data-aos-duration\": \"1200\",\n            \"data-aos-delay\": \"400\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-heading\",\n              children: [miniTitle && /*#__PURE__*/_jsxDEV(\"h6\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: miniTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this), title && /*#__PURE__*/_jsxDEV(\"h2\", {\n                children: parser(title)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"review-box\",\n              children: funfacts === null || funfacts === void 0 ? void 0 : funfacts.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"r-box\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [item.number, /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"btn-bar\",\n              children: /*#__PURE__*/_jsxDEV(ScrollLink, {\n                to: btnUrl,\n                spy: true,\n                smooth: true,\n                offset: -80,\n                duration: 300,\n                className: \"px-btn\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: btnText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this), ' ', /*#__PURE__*/_jsxDEV(\"i\", {\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    icon: \"bi:arrow-right\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["Icon", "React", "parser", "Link", "ScrollLink", "jsxDEV", "_jsxDEV", "About", "data", "imgSrc", "miniTitle", "title", "description", "funfacts", "btnText", "btnUrl", "className", "id", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "number", "to", "spy", "smooth", "offset", "duration", "icon", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/About.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React from 'react';\nimport parser from 'html-react-parser';\nimport { Link as ScrollLink } from 'react-scroll';\n\nexport default function About({ data }) {\n  const { imgSrc, miniTitle, title, description, funfacts, btnText, btnUrl } =\n    data;\n  return (\n    <section className=\"about-section section\" id=\"about\">\n      <div className=\"container\">\n        <div className=\"effect-1\">\n          <img\n            src=\"/images/effect-1.svg\"\n            alt=\"Shape\"\n            data-aos=\"zoom-in\"\n            data-aos-duration=\"1200\"\n            data-aos-delay=\"500\"\n          />\n        </div>\n        <div className=\"effect-2\">\n          <img\n            src=\"/images/effect-2.svg\"\n            alt=\"Shape\"\n            data-aos=\"zoom-in\"\n            data-aos-duration=\"1200\"\n            data-aos-delay=\"400\"\n          />\n        </div>\n        <div className=\"row align-items-center justify-content-center gy-5\">\n          <div\n            className=\"col-lg-6 col-xl-5\"\n            data-aos=\"fade-right\"\n            data-aos-duration=\"1200\"\n            data-aos-delay=\"500\"\n          >\n            <div className=\"about-banner text-center\">\n              <img src={imgSrc} alt=\"Thumb\" />\n            </div>\n          </div>\n          <div className=\"col-lg-6 col-xl-5 px-lg-5\">\n            <div\n              className=\"about-text\"\n              data-aos=\"fade\"\n              data-aos-duration=\"1200\"\n              data-aos-delay=\"400\"\n            >\n              <div className=\"section-heading\">\n                {miniTitle && (\n                  <h6>\n                    <span>{miniTitle}</span>\n                  </h6>\n                )}\n\n                {title && <h2>{parser(title)}</h2>}\n              </div>\n              <p>{description}</p>\n              <div className=\"review-box\">\n                {funfacts?.map((item, index) => (\n                  <div className=\"r-box\" key={index}>\n                    <h3>\n                      {item.number}\n                      <span>+</span>\n                    </h3>\n                    <label>{item.title}</label>\n                  </div>\n                ))}\n              </div>\n              <div className=\"btn-bar\">\n                <ScrollLink\n                  to={btnUrl}\n                  spy={true}\n                  smooth={true}\n                  offset={-80}\n                  duration={300}\n                  className=\"px-btn\"\n                >\n                  <span>{btnText}</span>{' '}\n                  <i>\n                    <Icon icon=\"bi:arrow-right\" />\n                  </i>\n                </ScrollLink>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,IAAI,IAAIC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,eAAe,SAASC,KAAKA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACtC,MAAM;IAAEC,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,OAAO;IAAEC;EAAO,CAAC,GACxEP,IAAI;EACN,oBACEF,OAAA;IAASU,SAAS,EAAC,uBAAuB;IAACC,EAAE,EAAC,OAAO;IAAAC,QAAA,eACnDZ,OAAA;MAAKU,SAAS,EAAC,WAAW;MAAAE,QAAA,gBACxBZ,OAAA;QAAKU,SAAS,EAAC,UAAU;QAAAE,QAAA,eACvBZ,OAAA;UACEa,GAAG,EAAC,sBAAsB;UAC1BC,GAAG,EAAC,OAAO;UACX,YAAS,SAAS;UAClB,qBAAkB,MAAM;UACxB,kBAAe;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlB,OAAA;QAAKU,SAAS,EAAC,UAAU;QAAAE,QAAA,eACvBZ,OAAA;UACEa,GAAG,EAAC,sBAAsB;UAC1BC,GAAG,EAAC,OAAO;UACX,YAAS,SAAS;UAClB,qBAAkB,MAAM;UACxB,kBAAe;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlB,OAAA;QAAKU,SAAS,EAAC,oDAAoD;QAAAE,QAAA,gBACjEZ,OAAA;UACEU,SAAS,EAAC,mBAAmB;UAC7B,YAAS,YAAY;UACrB,qBAAkB,MAAM;UACxB,kBAAe,KAAK;UAAAE,QAAA,eAEpBZ,OAAA;YAAKU,SAAS,EAAC,0BAA0B;YAAAE,QAAA,eACvCZ,OAAA;cAAKa,GAAG,EAAEV,MAAO;cAACW,GAAG,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlB,OAAA;UAAKU,SAAS,EAAC,2BAA2B;UAAAE,QAAA,eACxCZ,OAAA;YACEU,SAAS,EAAC,YAAY;YACtB,YAAS,MAAM;YACf,qBAAkB,MAAM;YACxB,kBAAe,KAAK;YAAAE,QAAA,gBAEpBZ,OAAA;cAAKU,SAAS,EAAC,iBAAiB;cAAAE,QAAA,GAC7BR,SAAS,iBACRJ,OAAA;gBAAAY,QAAA,eACEZ,OAAA;kBAAAY,QAAA,EAAOR;gBAAS;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACL,EAEAb,KAAK,iBAAIL,OAAA;gBAAAY,QAAA,EAAKhB,MAAM,CAACS,KAAK;cAAC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACNlB,OAAA;cAAAY,QAAA,EAAIN;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlB,OAAA;cAAKU,SAAS,EAAC,YAAY;cAAAE,QAAA,EACxBL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBrB,OAAA;gBAAKU,SAAS,EAAC,OAAO;gBAAAE,QAAA,gBACpBZ,OAAA;kBAAAY,QAAA,GACGQ,IAAI,CAACE,MAAM,eACZtB,OAAA;oBAAAY,QAAA,EAAM;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACLlB,OAAA;kBAAAY,QAAA,EAAQQ,IAAI,CAACf;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GALDG,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAM5B,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlB,OAAA;cAAKU,SAAS,EAAC,SAAS;cAAAE,QAAA,eACtBZ,OAAA,CAACF,UAAU;gBACTyB,EAAE,EAAEd,MAAO;gBACXe,GAAG,EAAE,IAAK;gBACVC,MAAM,EAAE,IAAK;gBACbC,MAAM,EAAE,CAAC,EAAG;gBACZC,QAAQ,EAAE,GAAI;gBACdjB,SAAS,EAAC,QAAQ;gBAAAE,QAAA,gBAElBZ,OAAA;kBAAAY,QAAA,EAAOJ;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAAC,GAAG,eAC1BlB,OAAA;kBAAAY,QAAA,eACEZ,OAAA,CAACN,IAAI;oBAACkC,IAAI,EAAC;kBAAgB;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACW,EAAA,GApFuB5B,KAAK;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}