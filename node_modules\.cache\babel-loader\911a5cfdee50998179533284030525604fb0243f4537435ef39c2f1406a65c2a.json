{"ast": null, "code": "import React from'react';import Slider from'react-slick';import{jsx as _jsx}from\"react/jsx-runtime\";export default function Brands(_ref){let{data}=_ref;var settings={dots:false,arrows:false,infinite:true,autoplay:true,autoplaySpeed:0,speed:3000,slidesToShow:5,slidesToScroll:1,initialSlide:0,cssEase:'linear',pauseOnHover:true,pauseOnFocus:true,responsive:[{breakpoint:1400,settings:{slidesToShow:4}},{breakpoint:1200,settings:{slidesToShow:3}},{breakpoint:600,settings:{slidesToShow:2}},{breakpoint:480,settings:{slidesToShow:2}}]};return/*#__PURE__*/_jsx(\"div\",{className:\"py-3 py-md-4 brand-section gray-bg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",\"data-aos\":\"fade\",\"data-aos-duration\":\"1200\",\"data-aos-delay\":\"500\",children:/*#__PURE__*/_jsx(Slider,{...settings,className:\"slider-gap-50\",children:data.map((item,index)=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"pt-3 pb-3 text-center d-flex align-items-center justify-content-center w-100\",children:item.url?/*#__PURE__*/_jsx(\"a\",{href:item.url,target:\"_blank\",rel:\"noopener noreferrer\",className:\"d-block w-100\",style:{textDecoration:'none'},children:/*#__PURE__*/_jsx(\"img\",{src:item.src,alt:item.alt,className:\"w-100\",style:{transition:'transform 0.3s ease',cursor:'pointer'},onMouseOver:e=>e.target.style.transform='scale(1.05)',onMouseOut:e=>e.target.style.transform='scale(1)'})}):/*#__PURE__*/_jsx(\"img\",{src:item.src,alt:item.alt,className:\"w-100\"})})},index))})})});}", "map": {"version": 3, "names": ["React", "Slide<PERSON>", "jsx", "_jsx", "Brands", "_ref", "data", "settings", "dots", "arrows", "infinite", "autoplay", "autoplaySpeed", "speed", "slidesToShow", "slidesToScroll", "initialSlide", "cssEase", "pauseOnHover", "pauseOnFocus", "responsive", "breakpoint", "className", "children", "map", "item", "index", "url", "href", "target", "rel", "style", "textDecoration", "src", "alt", "transition", "cursor", "onMouseOver", "e", "transform", "onMouseOut"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Brands.jsx"], "sourcesContent": ["import React from 'react';\nimport Slider from 'react-slick';\n\nexport default function Brands({ data }) {\n  var settings = {\n    dots: false,\n    arrows: false,\n    infinite: true,\n    autoplay: true,\n    autoplaySpeed: 0,\n    speed: 3000,\n    slidesToShow: 5,\n    slidesToScroll: 1,\n    initialSlide: 0,\n    cssEase: 'linear',\n    pauseOnHover: true,\n    pauseOnFocus: true,\n    responsive: [\n      {\n        breakpoint: 1400,\n        settings: {\n          slidesToShow: 4,\n        },\n      },\n      {\n        breakpoint: 1200,\n        settings: {\n          slidesToShow: 3,\n        },\n      },\n      {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 2,\n        },\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 2,\n        },\n      },\n    ],\n  };\n  return (\n    <div className=\"py-3 py-md-4 brand-section gray-bg\">\n      <div\n        className=\"container\"\n        data-aos=\"fade\"\n        data-aos-duration=\"1200\"\n        data-aos-delay=\"500\"\n      >\n        <Slider {...settings} className=\"slider-gap-50\">\n          {data.map((item, index) => (\n            <div key={index}>\n              <div className=\"pt-3 pb-3 text-center d-flex align-items-center justify-content-center w-100\">\n                {item.url ? (\n                  <a\n                    href={item.url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"d-block w-100\"\n                    style={{ textDecoration: 'none' }}\n                  >\n                    <img src={item.src} alt={item.alt} className=\"w-100\" style={{ transition: 'transform 0.3s ease', cursor: 'pointer' }}\n                         onMouseOver={(e) => e.target.style.transform = 'scale(1.05)'}\n                         onMouseOut={(e) => e.target.style.transform = 'scale(1)'} />\n                  </a>\n                ) : (\n                  <img src={item.src} alt={item.alt} className=\"w-100\" />\n                )}\n              </div>\n            </div>\n          ))}\n        </Slider>\n      </div>\n    </div>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEjC,cAAe,SAAS,CAAAC,MAAMA,CAAAC,IAAA,CAAW,IAAV,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACrC,GAAI,CAAAE,QAAQ,CAAG,CACbC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,KAAK,CACbC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,CAAC,CAChBC,KAAK,CAAE,IAAI,CACXC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,QAAQ,CACjBC,YAAY,CAAE,IAAI,CAClBC,YAAY,CAAE,IAAI,CAClBC,UAAU,CAAE,CACV,CACEC,UAAU,CAAE,IAAI,CAChBd,QAAQ,CAAE,CACRO,YAAY,CAAE,CAChB,CACF,CAAC,CACD,CACEO,UAAU,CAAE,IAAI,CAChBd,QAAQ,CAAE,CACRO,YAAY,CAAE,CAChB,CACF,CAAC,CACD,CACEO,UAAU,CAAE,GAAG,CACfd,QAAQ,CAAE,CACRO,YAAY,CAAE,CAChB,CACF,CAAC,CACD,CACEO,UAAU,CAAE,GAAG,CACfd,QAAQ,CAAE,CACRO,YAAY,CAAE,CAChB,CACF,CAAC,CAEL,CAAC,CACD,mBACEX,IAAA,QAAKmB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDpB,IAAA,QACEmB,SAAS,CAAC,WAAW,CACrB,WAAS,MAAM,CACf,oBAAkB,MAAM,CACxB,iBAAe,KAAK,CAAAC,QAAA,cAEpBpB,IAAA,CAACF,MAAM,KAAKM,QAAQ,CAAEe,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC5CjB,IAAI,CAACkB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACpBvB,IAAA,QAAAoB,QAAA,cACEpB,IAAA,QAAKmB,SAAS,CAAC,8EAA8E,CAAAC,QAAA,CAC1FE,IAAI,CAACE,GAAG,cACPxB,IAAA,MACEyB,IAAI,CAAEH,IAAI,CAACE,GAAI,CACfE,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBR,SAAS,CAAC,eAAe,CACzBS,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAAT,QAAA,cAElCpB,IAAA,QAAK8B,GAAG,CAAER,IAAI,CAACQ,GAAI,CAACC,GAAG,CAAET,IAAI,CAACS,GAAI,CAACZ,SAAS,CAAC,OAAO,CAACS,KAAK,CAAE,CAAEI,UAAU,CAAE,qBAAqB,CAAEC,MAAM,CAAE,SAAU,CAAE,CAChHC,WAAW,CAAGC,CAAC,EAAKA,CAAC,CAACT,MAAM,CAACE,KAAK,CAACQ,SAAS,CAAG,aAAc,CAC7DC,UAAU,CAAGF,CAAC,EAAKA,CAAC,CAACT,MAAM,CAACE,KAAK,CAACQ,SAAS,CAAG,UAAW,CAAE,CAAC,CAChE,CAAC,cAEJpC,IAAA,QAAK8B,GAAG,CAAER,IAAI,CAACQ,GAAI,CAACC,GAAG,CAAET,IAAI,CAACS,GAAI,CAACZ,SAAS,CAAC,OAAO,CAAE,CACvD,CACE,CAAC,EAjBEI,KAkBL,CACN,CAAC,CACI,CAAC,CACN,CAAC,CACH,CAAC,CAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}