{"ast": null, "code": "import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const limitRateError = () => {\n  return new EmailJSResponseStatus(429, 'Too Many Requests');\n};", "map": {"version": 3, "names": ["EmailJSResponseStatus", "limitRateError"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/errors/limitRateError/limitRateError.js"], "sourcesContent": ["import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const limitRateError = () => {\n    return new EmailJSResponseStatus(429, 'Too Many Requests');\n};\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,oCAAoC;AAC1E,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAChC,OAAO,IAAID,qBAAqB,CAAC,GAAG,EAAE,mBAAmB,CAAC;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}