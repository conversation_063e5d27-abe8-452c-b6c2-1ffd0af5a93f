{"ast": null, "code": "import React from'react';import{jsx as _jsx}from\"react/jsx-runtime\";export default function Footer(){return/*#__PURE__*/_jsx(\"footer\",{className:\"footer\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsx(\"p\",{className:\"m-0 text-center\",children:\"\\xA9 2025, Enamul Portfolio | Made with \\u2764\\uFE0F by Enamul Haq\"})})});}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "Footer", "className", "children"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"footer\">\n      <div className=\"container\">\n        <p className=\"m-0 text-center\">© 2025, Enamul Portfolio | Made with ❤️ by <PERSON><PERSON><PERSON></p>\n      </div>\n    </footer>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE1B,cAAe,SAAS,CAAAC,MAAMA,CAAA,CAAG,CAC/B,mBACED,IAAA,WAAQE,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACxBH,IAAA,QAAKE,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBH,IAAA,MAAGE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,oEAAqD,CAAG,CAAC,CACrF,CAAC,CACA,CAAC,CAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}