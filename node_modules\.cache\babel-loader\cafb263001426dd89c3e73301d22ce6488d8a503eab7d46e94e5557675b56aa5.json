{"ast": null, "code": "import{Route,Routes}from'react-router-dom';import Home from'./pages/Home';import Layout from'./components/Layout';import Aos from'aos';import'aos/dist/aos.css';import{useEffect}from'react';import{ToastContainer}from'react-toastify';import'react-toastify/dist/ReactToastify.css';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){useEffect(()=>{Aos.init({once:true,duration:1200,offset:100,delay:0});// Refresh AOS on window resize\nconst handleResize=()=>{Aos.refresh();};window.addEventListener('resize',handleResize);return()=>{window.removeEventListener('resize',handleResize);};},[]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Routes,{children:/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Layout,{}),children:/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Home,{})})})}),/*#__PURE__*/_jsx(ToastContainer,{position:\"top-right\",autoClose:5000,hideProgressBar:false,newestOnTop:false,closeOnClick:true,rtl:false,pauseOnFocusLoss:true,draggable:true,pauseOnHover:true,theme:\"light\"})]});}export default App;", "map": {"version": 3, "names": ["Route", "Routes", "Home", "Layout", "Aos", "useEffect", "ToastContainer", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "App", "init", "once", "duration", "offset", "delay", "handleResize", "refresh", "window", "addEventListener", "removeEventListener", "children", "path", "element", "index", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/App.js"], "sourcesContent": ["import { Route, Routes } from 'react-router-dom';\nimport Home from './pages/Home';\nimport Layout from './components/Layout';\nimport Aos from 'aos';\nimport 'aos/dist/aos.css';\nimport { useEffect } from 'react';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nfunction App() {\n  useEffect(() => {\n    Aos.init({\n      once: true,\n      duration: 1200,\n      offset: 100,\n      delay: 0,\n    });\n\n    // Refresh AOS on window resize\n    const handleResize = () => {\n      Aos.refresh();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n  return (\n    <>\n      <Routes>\n        <Route path=\"/\" element={<Layout />}>\n          <Route index element={<Home />} />\n        </Route>\n      </Routes>\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={5000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n    </>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,OAASA,KAAK,CAAEC,MAAM,KAAQ,kBAAkB,CAChD,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CACzB,OAASC,SAAS,KAAQ,OAAO,CACjC,OAASC,cAAc,KAAQ,gBAAgB,CAC/C,MAAO,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACbR,SAAS,CAAC,IAAM,CACdD,GAAG,CAACU,IAAI,CAAC,CACPC,IAAI,CAAE,IAAI,CACVC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,GAAG,CACXC,KAAK,CAAE,CACT,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBf,GAAG,CAACgB,OAAO,CAAC,CAAC,CACf,CAAC,CAEDC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAE/C,MAAO,IAAM,CACXE,MAAM,CAACE,mBAAmB,CAAC,QAAQ,CAAEJ,YAAY,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CACN,mBACEP,KAAA,CAAAF,SAAA,EAAAc,QAAA,eACEhB,IAAA,CAACP,MAAM,EAAAuB,QAAA,cACLhB,IAAA,CAACR,KAAK,EAACyB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElB,IAAA,CAACL,MAAM,GAAE,CAAE,CAAAqB,QAAA,cAClChB,IAAA,CAACR,KAAK,EAAC2B,KAAK,MAACD,OAAO,cAAElB,IAAA,CAACN,IAAI,GAAE,CAAE,CAAE,CAAC,CAC7B,CAAC,CACF,CAAC,cACTM,IAAA,CAACF,cAAc,EACbsB,QAAQ,CAAC,WAAW,CACpBC,SAAS,CAAE,IAAK,CAChBC,eAAe,CAAE,KAAM,CACvBC,WAAW,CAAE,KAAM,CACnBC,YAAY,MACZC,GAAG,CAAE,KAAM,CACXC,gBAAgB,MAChBC,SAAS,MACTC,YAAY,MACZC,KAAK,CAAC,OAAO,CACd,CAAC,EACF,CAAC,CAEP,CAEA,cAAe,CAAAxB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}