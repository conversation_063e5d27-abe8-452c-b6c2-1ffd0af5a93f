{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\SocialBtns.jsx\";\nimport { Icon } from '@iconify/react';\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function SocialBtns({\n  variant,\n  socialBtns\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `social-icon ${variant ? variant : ''}`,\n    children: socialBtns === null || socialBtns === void 0 ? void 0 : socialBtns.map((item, index) => /*#__PURE__*/_jsxDEV(Link, {\n      className: item.iconBgClass,\n      to: item.href,\n      target: \"_blank\",\n      children: /*#__PURE__*/_jsxDEV(Icon, {\n        icon: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 11\n      }, this)\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = SocialBtns;\nvar _c;\n$RefreshReg$(_c, \"SocialBtns\");", "map": {"version": 3, "names": ["Icon", "React", "Link", "jsxDEV", "_jsxDEV", "SocialBtns", "variant", "socialBtns", "className", "children", "map", "item", "index", "iconBgClass", "to", "href", "target", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/SocialBtns.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React from 'react';\nimport { Link } from 'react-router-dom';\n\nexport default function SocialBtns({ variant, socialBtns }) {\n  return (\n    <div\n      className={`social-icon ${variant ? variant : ''}`}\n    >\n      {socialBtns?.map((item, index) => (\n        <Link\n          className={item.iconBgClass}\n          to={item.href}\n          key={index}\n          target=\"_blank\"\n        >\n          <Icon icon={item.icon} />\n        </Link>\n      ))}\n    </div>\n  );\n}\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,UAAUA,CAAC;EAAEC,OAAO;EAAEC;AAAW,CAAC,EAAE;EAC1D,oBACEH,OAAA;IACEI,SAAS,EAAE,eAAeF,OAAO,GAAGA,OAAO,GAAG,EAAE,EAAG;IAAAG,QAAA,EAElDF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BR,OAAA,CAACF,IAAI;MACHM,SAAS,EAAEG,IAAI,CAACE,WAAY;MAC5BC,EAAE,EAAEH,IAAI,CAACI,IAAK;MAEdC,MAAM,EAAC,QAAQ;MAAAP,QAAA,eAEfL,OAAA,CAACJ,IAAI;QAACiB,IAAI,EAAEN,IAAI,CAACM;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC,GAHpBT,KAAK;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIN,CACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACC,EAAA,GAjBuBjB,UAAU;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}