{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\ContactInfo.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ContactInfo({\n  contactInfoData\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"contact-info\",\n    children: contactInfoData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"contact-info-in\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: item.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 11\n      }, this), item.email && /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `mailto:${item.email}`,\n        children: item.email\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 26\n      }, this), item.tel && /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `tel:${item.tel}`,\n        children: item.tel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 24\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = ContactInfo;\nvar _c;\n$RefreshReg$(_c, \"ContactInfo\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ContactInfo", "contactInfoData", "className", "children", "map", "item", "index", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "href", "tel", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/ContactInfo.jsx"], "sourcesContent": ["import React from 'react';\n\nexport default function ContactInfo({ contactInfoData }) {\n  return (\n    <div className=\"contact-info\">\n      {contactInfoData.map((item, index) => (\n        <div\n          className=\"contact-info-in\"\n          key={index}\n        >\n          <label>{item.title}</label>\n          {item.email && <a href={`mailto:${item.email}`}>{item.email}</a>}\n          {item.tel && <a href={`tel:${item.tel}`}>{item.tel}</a>}\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,eAAe,SAASC,WAAWA,CAAC;EAAEC;AAAgB,CAAC,EAAE;EACvD,oBACEF,OAAA;IAAKG,SAAS,EAAC,cAAc;IAAAC,QAAA,EAC1BF,eAAe,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/BP,OAAA;MACEG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAG3BJ,OAAA;QAAAI,QAAA,EAAQE,IAAI,CAACE;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC1BN,IAAI,CAACO,KAAK,iBAAIb,OAAA;QAAGc,IAAI,EAAE,UAAUR,IAAI,CAACO,KAAK,EAAG;QAAAT,QAAA,EAAEE,IAAI,CAACO;MAAK;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/DN,IAAI,CAACS,GAAG,iBAAIf,OAAA;QAAGc,IAAI,EAAE,OAAOR,IAAI,CAACS,GAAG,EAAG;QAAAX,QAAA,EAAEE,IAAI,CAACS;MAAG;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA,GAJlDL,KAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKP,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACI,EAAA,GAfuBf,WAAW;AAAA,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}