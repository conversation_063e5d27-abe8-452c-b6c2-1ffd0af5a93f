{"ast": null, "code": "import { store } from '../../store/store';\nimport { sendPost } from '../../api/sendPost';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\nimport { validateForm } from '../../utils/validateForm/validateForm';\nimport { validateParams } from '../../utils/validateParams/validateParams';\nimport { isHeadless } from '../../utils/isHeadless/isHeadless';\nimport { headlessError } from '../../errors/headlessError/headlessError';\nimport { isBlockedValueInParams } from '../../utils/isBlockedValueInParams/isBlockedValueInParams';\nimport { blockedEmailError } from '../../errors/blockedEmailError/blockedEmailError';\nimport { isLimitRateHit } from '../../utils/isLimitRateHit/isLimitRateHit';\nimport { limitRateError } from '../../errors/limitRateError/limitRateError';\nconst findHTMLForm = form => {\n  return typeof form === 'string' ? document.querySelector(form) : form;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {object} options - the EmailJS SDK config options\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = async (serviceID, templateID, form, options) => {\n  const opts = buildOptions(options);\n  const publicKey = opts.publicKey || store.publicKey;\n  const blockHeadless = opts.blockHeadless || store.blockHeadless;\n  const storageProvider = store.storageProvider || opts.storageProvider;\n  const blockList = {\n    ...store.blockList,\n    ...opts.blockList\n  };\n  const limitRate = {\n    ...store.limitRate,\n    ...opts.limitRate\n  };\n  if (blockHeadless && isHeadless(navigator)) {\n    return Promise.reject(headlessError());\n  }\n  const currentForm = findHTMLForm(form);\n  validateParams(publicKey, serviceID, templateID);\n  validateForm(currentForm);\n  const formData = new FormData(currentForm);\n  if (isBlockedValueInParams(blockList, formData)) {\n    return Promise.reject(blockedEmailError());\n  }\n  if (await isLimitRateHit(location.pathname, limitRate, storageProvider)) {\n    return Promise.reject(limitRateError());\n  }\n  formData.append('lib_version', '4.4.1');\n  formData.append('service_id', serviceID);\n  formData.append('template_id', templateID);\n  formData.append('user_id', publicKey);\n  return sendPost('/api/v1.0/email/send-form', formData);\n};", "map": {"version": 3, "names": ["store", "sendPost", "buildOptions", "validateForm", "validateParams", "isHeadless", "headlessError", "isBlockedValueInParams", "blockedEmailError", "isLimitRateHit", "limitRateError", "findHTMLForm", "form", "document", "querySelector", "sendForm", "serviceID", "templateID", "options", "opts", "public<PERSON>ey", "blockHeadless", "storageProvider", "blockList", "limitRate", "navigator", "Promise", "reject", "currentForm", "formData", "FormData", "location", "pathname", "append"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/@emailjs/browser/es/methods/sendForm/sendForm.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { sendPost } from '../../api/sendPost';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\nimport { validateForm } from '../../utils/validateForm/validateForm';\nimport { validateParams } from '../../utils/validateParams/validateParams';\nimport { isHeadless } from '../../utils/isHeadless/isHeadless';\nimport { headlessError } from '../../errors/headlessError/headlessError';\nimport { isBlockedValueInParams } from '../../utils/isBlockedValueInParams/isBlockedValueInParams';\nimport { blockedEmailError } from '../../errors/blockedEmailError/blockedEmailError';\nimport { isLimitRateHit } from '../../utils/isLimitRateHit/isLimitRateHit';\nimport { limitRateError } from '../../errors/limitRateError/limitRateError';\nconst findHTMLForm = (form) => {\n    return typeof form === 'string' ? document.querySelector(form) : form;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {object} options - the EmailJS SDK config options\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = async (serviceID, templateID, form, options) => {\n    const opts = buildOptions(options);\n    const publicKey = opts.publicKey || store.publicKey;\n    const blockHeadless = opts.blockHeadless || store.blockHeadless;\n    const storageProvider = store.storageProvider || opts.storageProvider;\n    const blockList = { ...store.blockList, ...opts.blockList };\n    const limitRate = { ...store.limitRate, ...opts.limitRate };\n    if (blockHeadless && isHeadless(navigator)) {\n        return Promise.reject(headlessError());\n    }\n    const currentForm = findHTMLForm(form);\n    validateParams(publicKey, serviceID, templateID);\n    validateForm(currentForm);\n    const formData = new FormData(currentForm);\n    if (isBlockedValueInParams(blockList, formData)) {\n        return Promise.reject(blockedEmailError());\n    }\n    if (await isLimitRateHit(location.pathname, limitRate, storageProvider)) {\n        return Promise.reject(limitRateError());\n    }\n    formData.append('lib_version', '4.4.1');\n    formData.append('service_id', serviceID);\n    formData.append('template_id', templateID);\n    formData.append('user_id', publicKey);\n    return sendPost('/api/v1.0/email/send-form', formData);\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,aAAa,QAAQ,0CAA0C;AACxE,SAASC,sBAAsB,QAAQ,2DAA2D;AAClG,SAASC,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,cAAc,QAAQ,4CAA4C;AAC3E,MAAMC,YAAY,GAAIC,IAAI,IAAK;EAC3B,OAAO,OAAOA,IAAI,KAAK,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAACF,IAAI,CAAC,GAAGA,IAAI;AACzE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,QAAQ,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,EAAEL,IAAI,EAAEM,OAAO,KAAK;EACpE,MAAMC,IAAI,GAAGjB,YAAY,CAACgB,OAAO,CAAC;EAClC,MAAME,SAAS,GAAGD,IAAI,CAACC,SAAS,IAAIpB,KAAK,CAACoB,SAAS;EACnD,MAAMC,aAAa,GAAGF,IAAI,CAACE,aAAa,IAAIrB,KAAK,CAACqB,aAAa;EAC/D,MAAMC,eAAe,GAAGtB,KAAK,CAACsB,eAAe,IAAIH,IAAI,CAACG,eAAe;EACrE,MAAMC,SAAS,GAAG;IAAE,GAAGvB,KAAK,CAACuB,SAAS;IAAE,GAAGJ,IAAI,CAACI;EAAU,CAAC;EAC3D,MAAMC,SAAS,GAAG;IAAE,GAAGxB,KAAK,CAACwB,SAAS;IAAE,GAAGL,IAAI,CAACK;EAAU,CAAC;EAC3D,IAAIH,aAAa,IAAIhB,UAAU,CAACoB,SAAS,CAAC,EAAE;IACxC,OAAOC,OAAO,CAACC,MAAM,CAACrB,aAAa,CAAC,CAAC,CAAC;EAC1C;EACA,MAAMsB,WAAW,GAAGjB,YAAY,CAACC,IAAI,CAAC;EACtCR,cAAc,CAACgB,SAAS,EAAEJ,SAAS,EAAEC,UAAU,CAAC;EAChDd,YAAY,CAACyB,WAAW,CAAC;EACzB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAACF,WAAW,CAAC;EAC1C,IAAIrB,sBAAsB,CAACgB,SAAS,EAAEM,QAAQ,CAAC,EAAE;IAC7C,OAAOH,OAAO,CAACC,MAAM,CAACnB,iBAAiB,CAAC,CAAC,CAAC;EAC9C;EACA,IAAI,MAAMC,cAAc,CAACsB,QAAQ,CAACC,QAAQ,EAAER,SAAS,EAAEF,eAAe,CAAC,EAAE;IACrE,OAAOI,OAAO,CAACC,MAAM,CAACjB,cAAc,CAAC,CAAC,CAAC;EAC3C;EACAmB,QAAQ,CAACI,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC;EACvCJ,QAAQ,CAACI,MAAM,CAAC,YAAY,EAAEjB,SAAS,CAAC;EACxCa,QAAQ,CAACI,MAAM,CAAC,aAAa,EAAEhB,UAAU,CAAC;EAC1CY,QAAQ,CAACI,MAAM,CAAC,SAAS,EAAEb,SAAS,CAAC;EACrC,OAAOnB,QAAQ,CAAC,2BAA2B,EAAE4B,QAAQ,CAAC;AAC1D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}