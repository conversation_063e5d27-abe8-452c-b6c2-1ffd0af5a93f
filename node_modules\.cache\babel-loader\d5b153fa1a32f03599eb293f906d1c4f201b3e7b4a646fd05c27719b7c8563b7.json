{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Experience.jsx\";\nimport React from 'react';\nimport SectionHeading from './SectionHeading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Experience({\n  data\n}) {\n  const {\n    sectionHeading,\n    allExperience\n  } = data;\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"section gray-bg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(SectionHeading, {\n        miniTitle: sectionHeading.miniTitle,\n        title: sectionHeading.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row gy-3\",\n        children: allExperience === null || allExperience === void 0 ? void 0 : allExperience.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ex-box\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row gy-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 col-lg-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ex-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: item.designation\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 24,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: item.company\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 25,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: item.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 26,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: item.jobType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 27,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 23,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-8 col-lg-9\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ex-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: item.companyTitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"m-0\",\n                    children: item.companyDescription\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 33,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_c = Experience;\nvar _c;\n$RefreshReg$(_c, \"Experience\");", "map": {"version": 3, "names": ["React", "SectionHeading", "jsxDEV", "_jsxDEV", "Experience", "data", "sectionHeading", "allExperience", "className", "children", "miniTitle", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "designation", "company", "duration", "jobType", "companyTitle", "companyDescription", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Experience.jsx"], "sourcesContent": ["import React from 'react';\nimport SectionHeading from './SectionHeading';\n\nexport default function Experience({ data }) {\n  const { sectionHeading, allExperience } = data;\n\n  return (\n    <section className=\"section gray-bg\">\n      <div className=\"container\">\n        <SectionHeading\n          miniTitle={sectionHeading.miniTitle}\n          title={sectionHeading.title}\n        />\n        <div className=\"row gy-3\">\n          {allExperience?.map((item, index) => (\n            <div\n              className=\"col-12\"\n              key={index}\n            >\n              <div className=\"ex-box\">\n                <div className=\"row gy-4\">\n                  <div className=\"col-md-4 col-lg-3\">\n                    <div className=\"ex-left\">\n                      <h4>{item.designation}</h4>\n                      <span>{item.company}</span>\n                      <p>{item.duration}</p>\n                      <label>{item.jobType}</label>\n                    </div>\n                  </div>\n                  <div className=\"col-md-8 col-lg-9\">\n                    <div className=\"ex-right\">\n                      <h5>{item.companyTitle}</h5>\n                      <p className=\"m-0\">{item.companyDescription}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,eAAe,SAASC,UAAUA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAC3C,MAAM;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGF,IAAI;EAE9C,oBACEF,OAAA;IAASK,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAClCN,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBN,OAAA,CAACF,cAAc;QACbS,SAAS,EAAEJ,cAAc,CAACI,SAAU;QACpCC,KAAK,EAAEL,cAAc,CAACK;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFZ,OAAA;QAAKK,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtBF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAES,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9Bf,OAAA;UACEK,SAAS,EAAC,QAAQ;UAAAC,QAAA,eAGlBN,OAAA;YAAKK,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrBN,OAAA;cAAKK,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBN,OAAA;gBAAKK,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChCN,OAAA;kBAAKK,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBN,OAAA;oBAAAM,QAAA,EAAKQ,IAAI,CAACE;kBAAW;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3BZ,OAAA;oBAAAM,QAAA,EAAOQ,IAAI,CAACG;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BZ,OAAA;oBAAAM,QAAA,EAAIQ,IAAI,CAACI;kBAAQ;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBZ,OAAA;oBAAAM,QAAA,EAAQQ,IAAI,CAACK;kBAAO;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNZ,OAAA;gBAAKK,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChCN,OAAA;kBAAKK,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBN,OAAA;oBAAAM,QAAA,EAAKQ,IAAI,CAACM;kBAAY;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5BZ,OAAA;oBAAGK,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAEQ,IAAI,CAACO;kBAAkB;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAnBDG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACU,EAAA,GAxCuBrB,UAAU;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}