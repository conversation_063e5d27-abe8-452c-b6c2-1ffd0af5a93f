{"ast": null, "code": "\"use strict\";\n\nvar __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n    desc = {\n      enumerable: true,\n      get: function () {\n        return m[k];\n      }\n    };\n  }\n  Object.defineProperty(o, k2, desc);\n} : function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\nvar __exportStar = this && this.__exportStar || function (m, exports) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DomHandler = void 0;\nvar domelementtype_1 = require(\"domelementtype\");\nvar node_js_1 = require(\"./node.js\");\n__exportStar(require(\"./node.js\"), exports);\n// Default options\nvar defaultOpts = {\n  withStartIndices: false,\n  withEndIndices: false,\n  xmlMode: false\n};\nvar DomHandler = /** @class */function () {\n  /**\n   * @param callback Called once parsing has completed.\n   * @param options Settings for the handler.\n   * @param elementCB Callback whenever a tag is closed.\n   */\n  function DomHandler(callback, options, elementCB) {\n    /** The elements of the DOM */\n    this.dom = [];\n    /** The root element for the DOM */\n    this.root = new node_js_1.Document(this.dom);\n    /** Indicated whether parsing has been completed. */\n    this.done = false;\n    /** Stack of open tags. */\n    this.tagStack = [this.root];\n    /** A data node that is still being written to. */\n    this.lastNode = null;\n    /** Reference to the parser instance. Used for location information. */\n    this.parser = null;\n    // Make it possible to skip arguments, for backwards-compatibility\n    if (typeof options === \"function\") {\n      elementCB = options;\n      options = defaultOpts;\n    }\n    if (typeof callback === \"object\") {\n      options = callback;\n      callback = undefined;\n    }\n    this.callback = callback !== null && callback !== void 0 ? callback : null;\n    this.options = options !== null && options !== void 0 ? options : defaultOpts;\n    this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n  }\n  DomHandler.prototype.onparserinit = function (parser) {\n    this.parser = parser;\n  };\n  // Resets the handler back to starting state\n  DomHandler.prototype.onreset = function () {\n    this.dom = [];\n    this.root = new node_js_1.Document(this.dom);\n    this.done = false;\n    this.tagStack = [this.root];\n    this.lastNode = null;\n    this.parser = null;\n  };\n  // Signals the handler that parsing is done\n  DomHandler.prototype.onend = function () {\n    if (this.done) return;\n    this.done = true;\n    this.parser = null;\n    this.handleCallback(null);\n  };\n  DomHandler.prototype.onerror = function (error) {\n    this.handleCallback(error);\n  };\n  DomHandler.prototype.onclosetag = function () {\n    this.lastNode = null;\n    var elem = this.tagStack.pop();\n    if (this.options.withEndIndices) {\n      elem.endIndex = this.parser.endIndex;\n    }\n    if (this.elementCB) this.elementCB(elem);\n  };\n  DomHandler.prototype.onopentag = function (name, attribs) {\n    var type = this.options.xmlMode ? domelementtype_1.ElementType.Tag : undefined;\n    var element = new node_js_1.Element(name, attribs, undefined, type);\n    this.addNode(element);\n    this.tagStack.push(element);\n  };\n  DomHandler.prototype.ontext = function (data) {\n    var lastNode = this.lastNode;\n    if (lastNode && lastNode.type === domelementtype_1.ElementType.Text) {\n      lastNode.data += data;\n      if (this.options.withEndIndices) {\n        lastNode.endIndex = this.parser.endIndex;\n      }\n    } else {\n      var node = new node_js_1.Text(data);\n      this.addNode(node);\n      this.lastNode = node;\n    }\n  };\n  DomHandler.prototype.oncomment = function (data) {\n    if (this.lastNode && this.lastNode.type === domelementtype_1.ElementType.Comment) {\n      this.lastNode.data += data;\n      return;\n    }\n    var node = new node_js_1.Comment(data);\n    this.addNode(node);\n    this.lastNode = node;\n  };\n  DomHandler.prototype.oncommentend = function () {\n    this.lastNode = null;\n  };\n  DomHandler.prototype.oncdatastart = function () {\n    var text = new node_js_1.Text(\"\");\n    var node = new node_js_1.CDATA([text]);\n    this.addNode(node);\n    text.parent = node;\n    this.lastNode = text;\n  };\n  DomHandler.prototype.oncdataend = function () {\n    this.lastNode = null;\n  };\n  DomHandler.prototype.onprocessinginstruction = function (name, data) {\n    var node = new node_js_1.ProcessingInstruction(name, data);\n    this.addNode(node);\n  };\n  DomHandler.prototype.handleCallback = function (error) {\n    if (typeof this.callback === \"function\") {\n      this.callback(error, this.dom);\n    } else if (error) {\n      throw error;\n    }\n  };\n  DomHandler.prototype.addNode = function (node) {\n    var parent = this.tagStack[this.tagStack.length - 1];\n    var previousSibling = parent.children[parent.children.length - 1];\n    if (this.options.withStartIndices) {\n      node.startIndex = this.parser.startIndex;\n    }\n    if (this.options.withEndIndices) {\n      node.endIndex = this.parser.endIndex;\n    }\n    parent.children.push(node);\n    if (previousSibling) {\n      node.prev = previousSibling;\n      previousSibling.next = node;\n    }\n    node.parent = parent;\n    this.lastNode = null;\n  };\n  return DomHandler;\n}();\nexports.DomHandler = DomHandler;\nexports.default = DomHandler;", "map": {"version": 3, "names": ["__createBinding", "Object", "create", "o", "m", "k", "k2", "undefined", "desc", "getOwnPropertyDescriptor", "__esModule", "writable", "configurable", "enumerable", "get", "defineProperty", "__exportStar", "exports", "p", "prototype", "hasOwnProperty", "call", "value", "<PERSON><PERSON><PERSON><PERSON>", "domelementtype_1", "require", "node_js_1", "defaultOpts", "withStartIndices", "withEndIndices", "xmlMode", "callback", "options", "elementCB", "dom", "root", "Document", "done", "tagStack", "lastNode", "parser", "onparserinit", "onreset", "onend", "handleCallback", "onerror", "error", "onclosetag", "elem", "pop", "endIndex", "onopentag", "name", "attribs", "type", "ElementType", "Tag", "element", "Element", "addNode", "push", "ontext", "data", "Text", "node", "oncomment", "Comment", "oncommentend", "oncdatastart", "text", "CDATA", "parent", "oncdataend", "onprocessinginstruction", "ProcessingInstruction", "length", "previousSibling", "children", "startIndex", "prev", "next", "default"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/domhandler/lib/index.js"], "sourcesContent": ["\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DomHandler = void 0;\nvar domelementtype_1 = require(\"domelementtype\");\nvar node_js_1 = require(\"./node.js\");\n__exportStar(require(\"./node.js\"), exports);\n// Default options\nvar defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nvar DomHandler = /** @class */ (function () {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    function DomHandler(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new node_js_1.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    DomHandler.prototype.onparserinit = function (parser) {\n        this.parser = parser;\n    };\n    // Resets the handler back to starting state\n    DomHandler.prototype.onreset = function () {\n        this.dom = [];\n        this.root = new node_js_1.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    };\n    // Signals the handler that parsing is done\n    DomHandler.prototype.onend = function () {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    };\n    DomHandler.prototype.onerror = function (error) {\n        this.handleCallback(error);\n    };\n    DomHandler.prototype.onclosetag = function () {\n        this.lastNode = null;\n        var elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    };\n    DomHandler.prototype.onopentag = function (name, attribs) {\n        var type = this.options.xmlMode ? domelementtype_1.ElementType.Tag : undefined;\n        var element = new node_js_1.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    };\n    DomHandler.prototype.ontext = function (data) {\n        var lastNode = this.lastNode;\n        if (lastNode && lastNode.type === domelementtype_1.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            var node = new node_js_1.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    };\n    DomHandler.prototype.oncomment = function (data) {\n        if (this.lastNode && this.lastNode.type === domelementtype_1.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        var node = new node_js_1.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    };\n    DomHandler.prototype.oncommentend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.oncdatastart = function () {\n        var text = new node_js_1.Text(\"\");\n        var node = new node_js_1.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    };\n    DomHandler.prototype.oncdataend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.onprocessinginstruction = function (name, data) {\n        var node = new node_js_1.ProcessingInstruction(name, data);\n        this.addNode(node);\n    };\n    DomHandler.prototype.handleCallback = function (error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    };\n    DomHandler.prototype.addNode = function (node) {\n        var parent = this.tagStack[this.tagStack.length - 1];\n        var previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    };\n    return DomHandler;\n}());\nexports.DomHandler = DomHandler;\nexports.default = DomHandler;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,KAAMC,MAAM,CAACC,MAAM,GAAI,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EAC5F,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5B,IAAIG,IAAI,GAAGP,MAAM,CAACQ,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC;EAChD,IAAI,CAACG,IAAI,KAAK,KAAK,IAAIA,IAAI,GAAG,CAACJ,CAAC,CAACM,UAAU,GAAGF,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACI,YAAY,CAAC,EAAE;IACjFJ,IAAI,GAAG;MAAEK,UAAU,EAAE,IAAI;MAAEC,GAAG,EAAE,SAAAA,CAAA,EAAW;QAAE,OAAOV,CAAC,CAACC,CAAC,CAAC;MAAE;IAAE,CAAC;EAC/D;EACAJ,MAAM,CAACc,cAAc,CAACZ,CAAC,EAAEG,EAAE,EAAEE,IAAI,CAAC;AACtC,CAAC,GAAK,UAASL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EACxB,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5BF,CAAC,CAACG,EAAE,CAAC,GAAGF,CAAC,CAACC,CAAC,CAAC;AAChB,CAAE,CAAC;AACH,IAAIW,YAAY,GAAI,IAAI,IAAI,IAAI,CAACA,YAAY,IAAK,UAASZ,CAAC,EAAEa,OAAO,EAAE;EACnE,KAAK,IAAIC,CAAC,IAAId,CAAC,EAAE,IAAIc,CAAC,KAAK,SAAS,IAAI,CAACjB,MAAM,CAACkB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,OAAO,EAAEC,CAAC,CAAC,EAAElB,eAAe,CAACiB,OAAO,EAAEb,CAAC,EAAEc,CAAC,CAAC;AAC7H,CAAC;AACDjB,MAAM,CAACc,cAAc,CAACE,OAAO,EAAE,YAAY,EAAE;EAAEK,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DL,OAAO,CAACM,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,gBAAgB,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAChD,IAAIC,SAAS,GAAGD,OAAO,CAAC,WAAW,CAAC;AACpCT,YAAY,CAACS,OAAO,CAAC,WAAW,CAAC,EAAER,OAAO,CAAC;AAC3C;AACA,IAAIU,WAAW,GAAG;EACdC,gBAAgB,EAAE,KAAK;EACvBC,cAAc,EAAE,KAAK;EACrBC,OAAO,EAAE;AACb,CAAC;AACD,IAAIP,UAAU,GAAG,aAAe,YAAY;EACxC;AACJ;AACA;AACA;AACA;EACI,SAASA,UAAUA,CAACQ,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;IAC9C;IACA,IAAI,CAACC,GAAG,GAAG,EAAE;IACb;IACA,IAAI,CAACC,IAAI,GAAG,IAAIT,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACF,GAAG,CAAC;IAC5C;IACA,IAAI,CAACG,IAAI,GAAG,KAAK;IACjB;IACA,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACH,IAAI,CAAC;IAC3B;IACA,IAAI,CAACI,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB;IACA,IAAI,OAAOR,OAAO,KAAK,UAAU,EAAE;MAC/BC,SAAS,GAAGD,OAAO;MACnBA,OAAO,GAAGL,WAAW;IACzB;IACA,IAAI,OAAOI,QAAQ,KAAK,QAAQ,EAAE;MAC9BC,OAAO,GAAGD,QAAQ;MAClBA,QAAQ,GAAGxB,SAAS;IACxB;IACA,IAAI,CAACwB,QAAQ,GAAGA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,IAAI;IAC1E,IAAI,CAACC,OAAO,GAAGA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGL,WAAW;IAC7E,IAAI,CAACM,SAAS,GAAGA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,IAAI;EAClF;EACAV,UAAU,CAACJ,SAAS,CAACsB,YAAY,GAAG,UAAUD,MAAM,EAAE;IAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB,CAAC;EACD;EACAjB,UAAU,CAACJ,SAAS,CAACuB,OAAO,GAAG,YAAY;IACvC,IAAI,CAACR,GAAG,GAAG,EAAE;IACb,IAAI,CAACC,IAAI,GAAG,IAAIT,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACF,GAAG,CAAC;IAC5C,IAAI,CAACG,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACH,IAAI,CAAC;IAC3B,IAAI,CAACI,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;EACtB,CAAC;EACD;EACAjB,UAAU,CAACJ,SAAS,CAACwB,KAAK,GAAG,YAAY;IACrC,IAAI,IAAI,CAACN,IAAI,EACT;IACJ,IAAI,CAACA,IAAI,GAAG,IAAI;IAChB,IAAI,CAACG,MAAM,GAAG,IAAI;IAClB,IAAI,CAACI,cAAc,CAAC,IAAI,CAAC;EAC7B,CAAC;EACDrB,UAAU,CAACJ,SAAS,CAAC0B,OAAO,GAAG,UAAUC,KAAK,EAAE;IAC5C,IAAI,CAACF,cAAc,CAACE,KAAK,CAAC;EAC9B,CAAC;EACDvB,UAAU,CAACJ,SAAS,CAAC4B,UAAU,GAAG,YAAY;IAC1C,IAAI,CAACR,QAAQ,GAAG,IAAI;IACpB,IAAIS,IAAI,GAAG,IAAI,CAACV,QAAQ,CAACW,GAAG,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACjB,OAAO,CAACH,cAAc,EAAE;MAC7BmB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACV,MAAM,CAACU,QAAQ;IACxC;IACA,IAAI,IAAI,CAACjB,SAAS,EACd,IAAI,CAACA,SAAS,CAACe,IAAI,CAAC;EAC5B,CAAC;EACDzB,UAAU,CAACJ,SAAS,CAACgC,SAAS,GAAG,UAAUC,IAAI,EAAEC,OAAO,EAAE;IACtD,IAAIC,IAAI,GAAG,IAAI,CAACtB,OAAO,CAACF,OAAO,GAAGN,gBAAgB,CAAC+B,WAAW,CAACC,GAAG,GAAGjD,SAAS;IAC9E,IAAIkD,OAAO,GAAG,IAAI/B,SAAS,CAACgC,OAAO,CAACN,IAAI,EAAEC,OAAO,EAAE9C,SAAS,EAAE+C,IAAI,CAAC;IACnE,IAAI,CAACK,OAAO,CAACF,OAAO,CAAC;IACrB,IAAI,CAACnB,QAAQ,CAACsB,IAAI,CAACH,OAAO,CAAC;EAC/B,CAAC;EACDlC,UAAU,CAACJ,SAAS,CAAC0C,MAAM,GAAG,UAAUC,IAAI,EAAE;IAC1C,IAAIvB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAACe,IAAI,KAAK9B,gBAAgB,CAAC+B,WAAW,CAACQ,IAAI,EAAE;MACjExB,QAAQ,CAACuB,IAAI,IAAIA,IAAI;MACrB,IAAI,IAAI,CAAC9B,OAAO,CAACH,cAAc,EAAE;QAC7BU,QAAQ,CAACW,QAAQ,GAAG,IAAI,CAACV,MAAM,CAACU,QAAQ;MAC5C;IACJ,CAAC,MACI;MACD,IAAIc,IAAI,GAAG,IAAItC,SAAS,CAACqC,IAAI,CAACD,IAAI,CAAC;MACnC,IAAI,CAACH,OAAO,CAACK,IAAI,CAAC;MAClB,IAAI,CAACzB,QAAQ,GAAGyB,IAAI;IACxB;EACJ,CAAC;EACDzC,UAAU,CAACJ,SAAS,CAAC8C,SAAS,GAAG,UAAUH,IAAI,EAAE;IAC7C,IAAI,IAAI,CAACvB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACe,IAAI,KAAK9B,gBAAgB,CAAC+B,WAAW,CAACW,OAAO,EAAE;MAC9E,IAAI,CAAC3B,QAAQ,CAACuB,IAAI,IAAIA,IAAI;MAC1B;IACJ;IACA,IAAIE,IAAI,GAAG,IAAItC,SAAS,CAACwC,OAAO,CAACJ,IAAI,CAAC;IACtC,IAAI,CAACH,OAAO,CAACK,IAAI,CAAC;IAClB,IAAI,CAACzB,QAAQ,GAAGyB,IAAI;EACxB,CAAC;EACDzC,UAAU,CAACJ,SAAS,CAACgD,YAAY,GAAG,YAAY;IAC5C,IAAI,CAAC5B,QAAQ,GAAG,IAAI;EACxB,CAAC;EACDhB,UAAU,CAACJ,SAAS,CAACiD,YAAY,GAAG,YAAY;IAC5C,IAAIC,IAAI,GAAG,IAAI3C,SAAS,CAACqC,IAAI,CAAC,EAAE,CAAC;IACjC,IAAIC,IAAI,GAAG,IAAItC,SAAS,CAAC4C,KAAK,CAAC,CAACD,IAAI,CAAC,CAAC;IACtC,IAAI,CAACV,OAAO,CAACK,IAAI,CAAC;IAClBK,IAAI,CAACE,MAAM,GAAGP,IAAI;IAClB,IAAI,CAACzB,QAAQ,GAAG8B,IAAI;EACxB,CAAC;EACD9C,UAAU,CAACJ,SAAS,CAACqD,UAAU,GAAG,YAAY;IAC1C,IAAI,CAACjC,QAAQ,GAAG,IAAI;EACxB,CAAC;EACDhB,UAAU,CAACJ,SAAS,CAACsD,uBAAuB,GAAG,UAAUrB,IAAI,EAAEU,IAAI,EAAE;IACjE,IAAIE,IAAI,GAAG,IAAItC,SAAS,CAACgD,qBAAqB,CAACtB,IAAI,EAAEU,IAAI,CAAC;IAC1D,IAAI,CAACH,OAAO,CAACK,IAAI,CAAC;EACtB,CAAC;EACDzC,UAAU,CAACJ,SAAS,CAACyB,cAAc,GAAG,UAAUE,KAAK,EAAE;IACnD,IAAI,OAAO,IAAI,CAACf,QAAQ,KAAK,UAAU,EAAE;MACrC,IAAI,CAACA,QAAQ,CAACe,KAAK,EAAE,IAAI,CAACZ,GAAG,CAAC;IAClC,CAAC,MACI,IAAIY,KAAK,EAAE;MACZ,MAAMA,KAAK;IACf;EACJ,CAAC;EACDvB,UAAU,CAACJ,SAAS,CAACwC,OAAO,GAAG,UAAUK,IAAI,EAAE;IAC3C,IAAIO,MAAM,GAAG,IAAI,CAACjC,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACqC,MAAM,GAAG,CAAC,CAAC;IACpD,IAAIC,eAAe,GAAGL,MAAM,CAACM,QAAQ,CAACN,MAAM,CAACM,QAAQ,CAACF,MAAM,GAAG,CAAC,CAAC;IACjE,IAAI,IAAI,CAAC3C,OAAO,CAACJ,gBAAgB,EAAE;MAC/BoC,IAAI,CAACc,UAAU,GAAG,IAAI,CAACtC,MAAM,CAACsC,UAAU;IAC5C;IACA,IAAI,IAAI,CAAC9C,OAAO,CAACH,cAAc,EAAE;MAC7BmC,IAAI,CAACd,QAAQ,GAAG,IAAI,CAACV,MAAM,CAACU,QAAQ;IACxC;IACAqB,MAAM,CAACM,QAAQ,CAACjB,IAAI,CAACI,IAAI,CAAC;IAC1B,IAAIY,eAAe,EAAE;MACjBZ,IAAI,CAACe,IAAI,GAAGH,eAAe;MAC3BA,eAAe,CAACI,IAAI,GAAGhB,IAAI;IAC/B;IACAA,IAAI,CAACO,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAChC,QAAQ,GAAG,IAAI;EACxB,CAAC;EACD,OAAOhB,UAAU;AACrB,CAAC,CAAC,CAAE;AACJN,OAAO,CAACM,UAAU,GAAGA,UAAU;AAC/BN,OAAO,CAACgE,OAAO,GAAG1D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}