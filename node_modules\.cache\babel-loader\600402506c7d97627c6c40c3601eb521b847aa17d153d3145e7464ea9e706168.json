{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\WhatsAppFloat.jsx\";\nimport React from 'react';\nimport { Icon } from '@iconify/react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function WhatsAppFloat() {\n  const phoneNumber = '+919006651039';\n  const message = 'Hello! I would like to connect with you.';\n  const handleWhatsAppClick = () => {\n    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;\n    window.open(whatsappUrl, '_blank');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"whatsapp-float\",\n    onClick: handleWhatsAppClick,\n    title: \"Chat with us on WhatsApp\",\n    children: /*#__PURE__*/_jsxDEV(Icon, {\n      icon: \"fa-brands:whatsapp\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = WhatsAppFloat;\nvar _c;\n$RefreshReg$(_c, \"WhatsAppFloat\");", "map": {"version": 3, "names": ["React", "Icon", "jsxDEV", "_jsxDEV", "WhatsAppFloat", "phoneNumber", "message", "handleWhatsAppClick", "whatsappUrl", "encodeURIComponent", "window", "open", "className", "onClick", "title", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/WhatsAppFloat.jsx"], "sourcesContent": ["import React from 'react';\nimport { Icon } from '@iconify/react';\n\nexport default function WhatsAppFloat() {\n  const phoneNumber = '+919006651039';\n  const message = 'Hello! I would like to connect with you.';\n  \n  const handleWhatsAppClick = () => {\n    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;\n    window.open(whatsappUrl, '_blank');\n  };\n\n  return (\n    <div \n      className=\"whatsapp-float\"\n      onClick={handleWhatsAppClick}\n      title=\"Chat with us on WhatsApp\"\n    >\n      <Icon icon=\"fa-brands:whatsapp\" />\n    </div>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,MAAMC,WAAW,GAAG,eAAe;EACnC,MAAMC,OAAO,GAAG,0CAA0C;EAE1D,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG,iBAAiBH,WAAW,SAASI,kBAAkB,CAACH,OAAO,CAAC,EAAE;IACtFI,MAAM,CAACC,IAAI,CAACH,WAAW,EAAE,QAAQ,CAAC;EACpC,CAAC;EAED,oBACEL,OAAA;IACES,SAAS,EAAC,gBAAgB;IAC1BC,OAAO,EAAEN,mBAAoB;IAC7BO,KAAK,EAAC,0BAA0B;IAAAC,QAAA,eAEhCZ,OAAA,CAACF,IAAI;MAACe,IAAI,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/B,CAAC;AAEV;AAACC,EAAA,GAlBuBjB,aAAa;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}