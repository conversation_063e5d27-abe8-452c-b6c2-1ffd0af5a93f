[{"C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\index.js": "1", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\App.js": "2", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\pages\\Home.jsx": "3", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Layout.jsx": "4", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Projects.jsx": "5", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\CustomCursor.jsx": "6", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Footer.jsx": "7", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Hero.jsx": "8", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Brands.jsx": "9", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Service.jsx": "10", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Testimonial.jsx": "11", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\About.jsx": "12", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Contact.jsx": "13", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Experience.jsx": "14", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Header.jsx": "15", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SectionHeading.jsx": "16", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Modal.jsx": "17", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Ratings.jsx": "18", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactInfo.jsx": "19", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SocialBtns.jsx": "20", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactForm.jsx": "21", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\WhatsAppFloat.jsx": "22"}, {"size": 442, "mtime": 1746806176002, "results": "23", "hashOfConfig": "24"}, {"size": 1235, "mtime": 1753979324967, "results": "25", "hashOfConfig": "24"}, {"size": 995, "mtime": 1746806177205, "results": "26", "hashOfConfig": "24"}, {"size": 419, "mtime": 1746806176506, "results": "27", "hashOfConfig": "24"}, {"size": 4377, "mtime": 1752248071613, "results": "28", "hashOfConfig": "24"}, {"size": 2237, "mtime": 1746806176705, "results": "29", "hashOfConfig": "24"}, {"size": 276, "mtime": 1746867224260, "results": "30", "hashOfConfig": "24"}, {"size": 2590, "mtime": 1746806176820, "results": "31", "hashOfConfig": "24"}, {"size": 2087, "mtime": 1750572997994, "results": "32", "hashOfConfig": "24"}, {"size": 1232, "mtime": 1752327899639, "results": "33", "hashOfConfig": "24"}, {"size": 1549, "mtime": 1752327925815, "results": "34", "hashOfConfig": "24"}, {"size": 2733, "mtime": 1746806176626, "results": "35", "hashOfConfig": "24"}, {"size": 1451, "mtime": 1752328346833, "results": "36", "hashOfConfig": "24"}, {"size": 1388, "mtime": 1752327912742, "results": "37", "hashOfConfig": "24"}, {"size": 3330, "mtime": 1746806176473, "results": "38", "hashOfConfig": "24"}, {"size": 463, "mtime": 1746806176927, "results": "39", "hashOfConfig": "24"}, {"size": 1953, "mtime": 1746806176661, "results": "40", "hashOfConfig": "24"}, {"size": 914, "mtime": 1746806176893, "results": "41", "hashOfConfig": "24"}, {"size": 486, "mtime": 1752328335453, "results": "42", "hashOfConfig": "24"}, {"size": 512, "mtime": 1752328325812, "results": "43", "hashOfConfig": "24"}, {"size": 5876, "mtime": 1752248056870, "results": "44", "hashOfConfig": "24"}, {"size": 583, "mtime": 1753979243498, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vebk8z", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\index.js", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\App.js", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\pages\\Home.jsx", ["112"], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Layout.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Projects.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\CustomCursor.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Footer.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Hero.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Brands.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Service.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Testimonial.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\About.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Contact.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Experience.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Header.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SectionHeading.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Modal.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Ratings.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactInfo.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SocialBtns.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactForm.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\WhatsAppFloat.jsx", [], [], {"ruleId": "113", "severity": 1, "message": "114", "line": 1, "column": 17, "nodeType": "115", "messageId": "116", "endLine": 1, "endColumn": 26}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar"]