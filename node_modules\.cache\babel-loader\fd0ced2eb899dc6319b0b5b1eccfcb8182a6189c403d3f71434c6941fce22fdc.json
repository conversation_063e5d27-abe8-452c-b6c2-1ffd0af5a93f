{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar Events = {\n  registered: {},\n  scrollEvent: {\n    register: function register(evtName, callback) {\n      Events.registered[evtName] = callback;\n    },\n    remove: function remove(evtName) {\n      Events.registered[evtName] = null;\n    }\n  }\n};\nexports.default = Events;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Events", "registered", "scrollEvent", "register", "evtName", "callback", "remove", "default"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/node_modules/react-scroll/modules/mixins/scroll-events.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar Events = {\n\tregistered: {},\n\tscrollEvent: {\n\t\tregister: function register(evtName, callback) {\n\t\t\tEvents.registered[evtName] = callback;\n\t\t},\n\t\tremove: function remove(evtName) {\n\t\t\tEvents.registered[evtName] = null;\n\t\t}\n\t}\n};\n\nexports.default = Events;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,MAAM,GAAG;EACZC,UAAU,EAAE,CAAC,CAAC;EACdC,WAAW,EAAE;IACZC,QAAQ,EAAE,SAASA,QAAQA,CAACC,OAAO,EAAEC,QAAQ,EAAE;MAC9CL,MAAM,CAACC,UAAU,CAACG,OAAO,CAAC,GAAGC,QAAQ;IACtC,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACF,OAAO,EAAE;MAChCJ,MAAM,CAACC,UAAU,CAACG,OAAO,CAAC,GAAG,IAAI;IAClC;EACD;AACD,CAAC;AAEDN,OAAO,CAACS,OAAO,GAAGP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}