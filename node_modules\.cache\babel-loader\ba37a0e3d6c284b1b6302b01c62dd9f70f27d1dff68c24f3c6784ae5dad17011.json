{"ast": null, "code": "var _jsxFileName = \"C:\\\\save\\\\Desktop\\\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\\\jenna-react\\\\src\\\\components\\\\Projects.jsx\",\n  _s = $RefreshSig$();\nimport { Icon } from '@iconify/react';\nimport React, { useState } from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\nimport Modal from './Modal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function Projects({\n  data\n}) {\n  _s();\n  const [modal, setModal] = useState(false);\n  const [modalType, setModalType] = useState('image');\n  const [modalData, setModalData] = useState({});\n  const {\n    sectionHeading,\n    allProjects\n  } = data;\n  const handelProjectDetails = (item, itemType) => {\n    if (itemType === 'image') {\n      setModalData(item);\n    } else {\n      setModalData(item);\n    }\n    setModalType(itemType);\n    setModal(!modal);\n    console.log(modalType);\n  };\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: true,\n    autoplaySpeed: 4000,\n    speed: 1000,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0,\n    variableWidth: true,\n    pauseOnHover: true,\n    pauseOnFocus: true,\n    cssEase: 'ease-in-out'\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"project-section section gray-bg\",\n      id: \"project\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(SectionHeading, {\n          miniTitle: sectionHeading.miniTitle,\n          title: sectionHeading.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"full-width\",\n          \"data-aos\": \"fade\",\n          \"data-aos-duration\": \"1200\",\n          \"data-aos-delay\": \"400\",\n          children: /*#__PURE__*/_jsxDEV(Slider, {\n            ...settings,\n            className: \"slider-gap-24\",\n            children: allProjects === null || allProjects === void 0 ? void 0 : allProjects.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '416px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-box\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"project-media\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.thumbUrl,\n                    alt: \"Thumb\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"gallery-link\",\n                    onClick: () => handelProjectDetails(item, 'image'),\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"bi:plus\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 65,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 64,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"project-body\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: item.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 71,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: item.subTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 72,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 70,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"link\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"p-link\",\n                      onClick: () => handelProjectDetails(item, 'details'),\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"bi:arrow-right\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 79,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 75,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 74,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), modal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mfp-wrap\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mfp-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mfp-bg\",\n          onClick: () => setModal(!modal)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mfp-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"mfp-close\",\n            onClick: () => setModal(!modal),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), modalType === 'image' ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: modalData.thumbUrl,\n            alt: \"Thumbnail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Modal, {\n            modalData: modalData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(Projects, \"42StFSeQ+EgCeAvX6gs9wqvg6zQ=\");\n_c = Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["Icon", "React", "useState", "SectionHeading", "Slide<PERSON>", "Modal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Projects", "data", "_s", "modal", "setModal", "modalType", "setModalType", "modalData", "setModalData", "sectionHeading", "allProjects", "handelProjectDetails", "item", "itemType", "console", "log", "settings", "dots", "arrows", "infinite", "autoplay", "autoplaySpeed", "speed", "slidesToShow", "slidesToScroll", "initialSlide", "variableWidth", "pauseOnHover", "pauseOnFocus", "cssEase", "children", "className", "id", "miniTitle", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "style", "width", "src", "thumbUrl", "alt", "onClick", "icon", "subTitle", "type", "_c", "$RefreshReg$"], "sources": ["C:/save/Desktop/jenna-personal-portfolio-template-2023-11-27-05-07-17-utc/jenna-react/src/components/Projects.jsx"], "sourcesContent": ["import { Icon } from '@iconify/react';\nimport React, { useState } from 'react';\nimport SectionHeading from './SectionHeading';\nimport Slider from 'react-slick';\nimport Modal from './Modal';\n\nexport default function Projects({ data }) {\n  const [modal, setModal] = useState(false);\n  const [modalType, setModalType] = useState('image');\n  const [modalData, setModalData] = useState({});\n  const { sectionHeading, allProjects } = data;\n  const handelProjectDetails = (item, itemType) => {\n    if (itemType === 'image') {\n      setModalData(item);\n    } else {\n      setModalData(item);\n    }\n    setModalType(itemType);\n\n    setModal(!modal);\n    console.log(modalType);\n  };\n\n  var settings = {\n    dots: true,\n    arrows: false,\n    infinite: true,\n    autoplay: true,\n    autoplaySpeed: 4000,\n    speed: 1000,\n    slidesToShow: 1,\n    slidesToScroll: 1,\n    initialSlide: 0,\n    variableWidth: true,\n    pauseOnHover: true,\n    pauseOnFocus: true,\n    cssEase: 'ease-in-out',\n  };\n\n  return (\n    <>\n      <section className=\"project-section section gray-bg\" id=\"project\">\n        <div className=\"container\">\n          <SectionHeading\n            miniTitle={sectionHeading.miniTitle}\n            title={sectionHeading.title}\n          />\n          <div\n            className=\"full-width\"\n            data-aos=\"fade\"\n            data-aos-duration=\"1200\"\n            data-aos-delay=\"400\"\n          >\n            <Slider {...settings} className=\"slider-gap-24\">\n              {allProjects?.map((item, index) => (\n                <div key={index} style={{ width: '416px' }}>\n                  <div className=\"project-box\">\n                    <div className=\"project-media\">\n                      <img src={item.thumbUrl} alt=\"Thumb\" />\n                      <span\n                        className=\"gallery-link\"\n                        onClick={() => handelProjectDetails(item, 'image')}\n                      >\n                        <i>\n                          <Icon icon=\"bi:plus\" />\n                        </i>\n                      </span>\n                    </div>\n                    <div className=\"project-body\">\n                      <div className=\"text\">\n                        <h5>{item.title}</h5>\n                        <span>{item.subTitle}</span>\n                      </div>\n                      <div className=\"link\">\n                        <span\n                          className=\"p-link\"\n                          onClick={() => handelProjectDetails(item, 'details')}\n                        >\n                          <Icon icon=\"bi:arrow-right\" />\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </Slider>\n          </div>\n        </div>\n      </section>\n      {modal && (\n        <div className=\"mfp-wrap\">\n          <div className=\"mfp-container\">\n            <div className=\"mfp-bg\" onClick={() => setModal(!modal)}></div>\n            <div className=\"mfp-content\">\n              <button\n                type=\"button\"\n                className=\"mfp-close\"\n                onClick={() => setModal(!modal)}\n              >\n                ×\n              </button>\n              {modalType === 'image' ? (\n                <img src={modalData.thumbUrl} alt=\"Thumbnail\" />\n              ) : (\n                <Modal modalData={modalData} />\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,KAAK,MAAM,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5B,eAAe,SAASC,QAAQA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EACzC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM;IAAEiB,cAAc;IAAEC;EAAY,CAAC,GAAGT,IAAI;EAC5C,MAAMU,oBAAoB,GAAGA,CAACC,IAAI,EAAEC,QAAQ,KAAK;IAC/C,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxBL,YAAY,CAACI,IAAI,CAAC;IACpB,CAAC,MAAM;MACLJ,YAAY,CAACI,IAAI,CAAC;IACpB;IACAN,YAAY,CAACO,QAAQ,CAAC;IAEtBT,QAAQ,CAAC,CAACD,KAAK,CAAC;IAChBW,OAAO,CAACC,GAAG,CAACV,SAAS,CAAC;EACxB,CAAC;EAED,IAAIW,QAAQ,GAAG;IACbC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE;EACX,CAAC;EAED,oBACEhC,OAAA,CAAAE,SAAA;IAAA+B,QAAA,gBACEjC,OAAA;MAASkC,SAAS,EAAC,iCAAiC;MAACC,EAAE,EAAC,SAAS;MAAAF,QAAA,eAC/DjC,OAAA;QAAKkC,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBjC,OAAA,CAACJ,cAAc;UACbwC,SAAS,EAAExB,cAAc,CAACwB,SAAU;UACpCC,KAAK,EAAEzB,cAAc,CAACyB;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFzC,OAAA;UACEkC,SAAS,EAAC,YAAY;UACtB,YAAS,MAAM;UACf,qBAAkB,MAAM;UACxB,kBAAe,KAAK;UAAAD,QAAA,eAEpBjC,OAAA,CAACH,MAAM;YAAA,GAAKsB,QAAQ;YAAEe,SAAS,EAAC,eAAe;YAAAD,QAAA,EAC5CpB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6B,GAAG,CAAC,CAAC3B,IAAI,EAAE4B,KAAK,kBAC5B3C,OAAA;cAAiB4C,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAQ,CAAE;cAAAZ,QAAA,eACzCjC,OAAA;gBAAKkC,SAAS,EAAC,aAAa;gBAAAD,QAAA,gBAC1BjC,OAAA;kBAAKkC,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5BjC,OAAA;oBAAK8C,GAAG,EAAE/B,IAAI,CAACgC,QAAS;oBAACC,GAAG,EAAC;kBAAO;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCzC,OAAA;oBACEkC,SAAS,EAAC,cAAc;oBACxBe,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAACC,IAAI,EAAE,OAAO,CAAE;oBAAAkB,QAAA,eAEnDjC,OAAA;sBAAAiC,QAAA,eACEjC,OAAA,CAACP,IAAI;wBAACyD,IAAI,EAAC;sBAAS;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNzC,OAAA;kBAAKkC,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBAC3BjC,OAAA;oBAAKkC,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBjC,OAAA;sBAAAiC,QAAA,EAAKlB,IAAI,CAACsB;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrBzC,OAAA;sBAAAiC,QAAA,EAAOlB,IAAI,CAACoC;oBAAQ;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACNzC,OAAA;oBAAKkC,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnBjC,OAAA;sBACEkC,SAAS,EAAC,QAAQ;sBAClBe,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAACC,IAAI,EAAE,SAAS,CAAE;sBAAAkB,QAAA,eAErDjC,OAAA,CAACP,IAAI;wBAACyD,IAAI,EAAC;sBAAgB;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA3BEE,KAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACTnC,KAAK,iBACJN,OAAA;MAAKkC,SAAS,EAAC,UAAU;MAAAD,QAAA,eACvBjC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC5BjC,OAAA;UAAKkC,SAAS,EAAC,QAAQ;UAACe,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAAC,CAACD,KAAK;QAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/DzC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1BjC,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACblB,SAAS,EAAC,WAAW;YACrBe,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAAC,CAACD,KAAK,CAAE;YAAA2B,QAAA,EACjC;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRjC,SAAS,KAAK,OAAO,gBACpBR,OAAA;YAAK8C,GAAG,EAAEpC,SAAS,CAACqC,QAAS;YAACC,GAAG,EAAC;UAAW;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhDzC,OAAA,CAACF,KAAK;YAACY,SAAS,EAAEA;UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC/B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP;AAACpC,EAAA,CA1GuBF,QAAQ;AAAAkD,EAAA,GAARlD,QAAQ;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}